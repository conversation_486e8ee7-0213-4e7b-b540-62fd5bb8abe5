import os
import configparser
import json
import requests
import mysql.connector
from mysql.connector import Error
import re
import sys
import csv
import glob
import time
import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
import pandas as pd
import talib
import matplotlib.ticker as ticker
import platform
import subprocess
import threading
import webbrowser
import random
import concurrent.futures
# # import queue  # 不再需要队列，改用预分配任务  # 不再需要队列
import logging  # 添加logging模块导入
from tkinter import ttk
from tkinter import Menu
from datetime import datetime
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.font_manager import FontProperties
from prettytable import PrettyTable
from tkinter import filedialog, ttk, Menu
import zipfile
import shutil
from bs4 import BeautifulSoup
import sqlite3


# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('stock_app.log')
    ]
)

# 禁止 matplotlib.category 的信息性消息
logging.getLogger('matplotlib.category').setLevel(logging.WARNING)


# 设置支持中文的字体
font_path = 'SimHei.ttf'  # 确保这个路径是正确的
font_prop = FontProperties(fname=font_path)
# 定义全局变量，用于存储程序文件当前所在目录
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
# 定义全局变量，用于存储数据库文件的路径
# MySQL数据库配置
MYSQL_CONFIG = {
    'host': '*************',
    'user': 'MYSQL_9SHENG',
    'password': '9Sheng_SQL',
    'database': 'UNRAID_MYSQL'
}

class StockApp:
    def __init__(self, root):
        self.root = root
    
        # 设置 matplotlib 日志级别
        logging.getLogger('matplotlib.category').setLevel(logging.WARNING)
        logging.getLogger('matplotlib').setLevel(logging.WARNING)
        

        # 添加缓存字典
        self.data_cache = {}
        self.cache_timeout = 300  # 缓存超时时间（秒）
        
        # 初始化资源字典，用于管理需要在程序关闭时清理的资源
        self.resources = {}

        # 创建全局数据库连接
        try:
            self.conn = mysql.connector.connect(
                host='*************',
                user='MYSQL_9SHENG',
                password='9Sheng_SQL',
                database='UNRAID_MYSQL'
            )
            self.cursor = self.conn.cursor()
        except Error as e:
            print(f"连接到MySQL数据库时出错: {e}")
            raise

        self.root.title("股票内部分析系统 - 作者:追寻小眼镜 - 2025.01.19")

        # 获取屏幕分辨率并计算自适应窗口高度
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算窗口高度：屏幕高度的90%，但最低900像素
        calculated_height = int(screen_height * 0.8)
        window_height = max(calculated_height, 900)  # 确保最低高度为900

        # 设置窗口宽度为1280（保持不变）
        window_width = 1280

        # 计算窗口居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height ) // 2 - 40

        # 设置窗口大小和位置
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")


        # 打印屏幕信息（调试用）
        print(f"屏幕分辨率: {screen_width}x{screen_height}")
        print(f"计算高度: {calculated_height} (屏幕高度的80%)")
        print(f"实际窗口高度: {window_height}")
        print(f"窗口位置: {x}, {y}")

        # 顶部框架（用于股票代码标签、输入框和显示股票分析图按键）
        self.top_frame = tk.Frame(root)
        self.top_frame.pack(side=tk.TOP, fill=tk.X, padx=5, pady=5)

        # 股票代码标签
        self.stock_id_label = tk.Label(self.top_frame, text="股票代码或名称:")
        self.stock_id_label.pack(side=tk.LEFT, padx=5, pady=5)

        # 定义输入框
        self.stock_id_var = tk.StringVar(value="000002")
        self.stock_id_entry = tk.Entry(self.top_frame, textvariable=self.stock_id_var, width=9, font=('SimSun', 20), justify='center')
        self.stock_id_entry.pack(side=tk.LEFT, padx=5, pady=5)
        # 绑定回车键事件到输入框
        self.stock_id_entry.bind("<Return>", self.on_enter_key_pressed)

        # 显示股票分析图按键
        self.plot_button = tk.Button(self.top_frame, text="显示分析图", command=self.on_plot_button_click)
        self.plot_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.download_data_button = tk.Button(self.top_frame, text="下载数据", command=self.on_download_data_click)
        self.download_data_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.get_stock_data_button = tk.Button(self.top_frame, text="①收盘后下载股票数据", command=self.on_get_stock_data_click)
        self.get_stock_data_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.autoplot_button = tk.Button(self.top_frame, text="②一键分析自选股", command=self.One_click_analysis_of_stock_selection)
        self.autoplot_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.switch_var = tk.IntVar()
        self.switch_checkbox = tk.Checkbutton(self.top_frame, text="初始化交易策略", variable=self.switch_var, command=self.check_switch_state)
        self.switch_checkbox.pack(side=tk.LEFT, padx=5, pady=5)

        # 创建标签：选择周期部分
        self.percentagesum_label = tk.Label(self.top_frame, text="当日策略持股收益[0%]  策略卖出回避亏损[0%]")
        self.percentagesum_label.pack(side=tk.LEFT, padx=5, pady=5)

        # 左侧框架（包括股票列表、选择周期的下拉菜单和滚动条）
        self.left_frame = tk.Frame(root)
        self.left_frame.pack(side=tk.LEFT, fill=tk.Y)

        # 创建一个新的框架用于水平排列
        self.period_frame = tk.Frame(self.left_frame)
        self.period_frame.pack(side=tk.TOP, padx=5, pady=5)

         # 选择自选股票列表文件
        self.Table_var = tk.StringVar(value="Table.txt")
        self.Table_menu = ttk.Combobox(self.period_frame, textvariable=self.Table_var, width=14, state="readonly")
        self.Table_menu.pack(side=tk.LEFT, padx=5, pady=5)
         
        # 加载文件列表
        self.load_table_files()

        # 创建标签：选择周期部分
        self.period_label = tk.Label(self.period_frame, text="交易日:")
        self.period_label.pack(side=tk.LEFT, padx=5, pady=5)

        # 创建下拉框：显示天数
        self.period_var = tk.StringVar(value="60")
        self.period_menu = ttk.Combobox(self.period_frame, textvariable=self.period_var, width=3, state="readonly")
        self.period_menu['values'] = ("30", "45", "60", "90", "120")
        self.period_menu.pack(side=tk.LEFT, padx=5, pady=5)


        # 创建一个新的框架用于包含股票列表框和滚动条
        self.stock_frame = tk.Frame(self.left_frame)
        self.stock_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)


        # 创建滚动条
        self.scrollbar = ttk.Scrollbar(self.stock_frame, orient=tk.VERTICAL)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建股票列表Treeview
        self.stock_tree = ttk.Treeview(self.stock_frame, columns=("code", "name", "change", "industry"), show="headings", yscrollcommand=self.scrollbar.set)
        self.stock_tree.heading("code", text="股票代码")
        self.stock_tree.heading("name", text="股票名称")
        self.stock_tree.heading("change", text="股价|涨跌")
        self.stock_tree.heading("industry", text="所属行业")
        self.stock_tree.column("code", width=50, anchor=tk.CENTER)       #E表示右对齐， W表示左对齐
        self.stock_tree.column("name", width=54, anchor=tk.W)
        self.stock_tree.column("change", width=80, anchor=tk.CENTER)
        self.stock_tree.column("industry", width=52, anchor=tk.W)
        # 添加自定义标签颜色配置
        self.stock_tree.tag_configure('up', foreground='red')
        self.stock_tree.tag_configure('down', foreground='green')
        self.stock_tree.tag_configure('normal', foreground='black')
    
        self.stock_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.config(command=self.stock_tree.yview)

        # 创建右键菜单，添加菜单项及其关联的处理函数。
        self.menu = Menu(self.root, tearoff=0)
        self.menu.add_command(label="访问《东方财富网》", command=self.get_www_eastmoney_com)
        self.menu.add_command(label="访问《同花顺》", command=self.get_www_0033_com)
        self.menu.add_separator()
        self.menu.add_command(label="分析该股交易策略", command=self.Analyze_stock_data_menu)
        self.menu.add_separator()
        self.menu.add_command(label="编辑当前列表文件", command=self.edit_stock_config_list)
        self.menu.add_separator()
        self.menu.add_command(label="统计策略当日收益", command=self.calculate_percentage_sum)

        # 绑定Treeview控件的右键单击事件，以显示右键菜单。
        self.stock_tree.bind("<Button-3>", self.show_menu)


        selected_file = self.get_selected_file()
        print(f"当前自选配置列表文件是: {selected_file}")
        self.load_stock_data(selected_file)

        self.stock_tree.bind("<<TreeviewSelect>>", self.show_stock_info)
        self.Table_menu.bind("<<ComboboxSelected>>", self.on_combobox_select)
    
        self.right_frame = tk.Frame(root)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        self.figure = plt.Figure(figsize=(10, 7.5))
        self.figure_canvas = FigureCanvasTkAgg(self.figure, master=self.right_frame)
        self.figure_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        self.stop_event = threading.Event()
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def __del__(self):
        try:
            if hasattr(self, 'conn'):
                self.conn.close()
                print("数据库连接已关闭")
        except Exception as e:
            print(f"关闭数据库连接时出错: {e}")


    def save_to_config(self, file_name):
        config = configparser.ConfigParser()
        config['DEFAULT'] = {'LastSelectedFile': file_name}
        with open('config.ini', 'w') as configfile:
            config.write(configfile)

    def load_from_config(self):
        config = configparser.ConfigParser()
        if os.path.exists('config.ini'):
            config.read('config.ini')
            return config['DEFAULT'].get('LastSelectedFile', None)
        return None
       
    def on_combobox_select(self, event):
        # 绑定自选股配置文件列表点击功能
        selected_file = self.Table_menu.get()
        print(f"当前选中的文件是: {selected_file}")
        # self.load_stock_data(selected_file)
        self.save_to_config(selected_file)  # 保存当前选择的文件到配置
        self.load_stock_data(selected_file)  # 加载自选股列表数据


    def calculate_percentage_sum(self):
        total_percentage_red = 0.0
        total_percentage_green = 0.0
        total_percentage_black = 0.0
        for item in self.stock_tree.get_children():
            tags = self.stock_tree.item(item, "tags")
            values = self.stock_tree.item(item, "values")
            # 取出第三列的内容，即 '[8252.87] -1.24%'
            data_str = values[2]
            # 按 '|' 分割数据
            data_parts = data_str.split('/')
            # 如果按 '|' 分割后部分数量小于2，尝试用其他分隔符
            if len(data_parts) < 2:
                data_parts = data_str.split('/')

            if len(data_parts) < 2:
                data_parts = data_str.split()

            if len(data_parts) == 2:
                # 提取并检查 percentage_str 是否为有效的浮点数
                percentage_str = data_parts[1].strip()
                try:
                    percentage = float(percentage_str.strip('%'))
                except ValueError:
                    print(f"无法转换为浮点数：'{percentage_str}'")
                    continue  # 跳过此循环，继续处理下一个 item

                if 'red' in tags:
                    total_percentage_red += percentage
                elif 'green' in tags:
                    total_percentage_green += percentage
                elif 'black' in tags:
                    total_percentage_black += percentage

        print(f"列表策略 红色盈亏[{total_percentage_red:.2f}%]  绿色盈亏[{total_percentage_green:.2f}%  黑色盈亏[{total_percentage_black:.2f}%]")
        print("-"*60)
        # 更新标签文本
        self.percentagesum_label.config(text=f"列表策略 红色盈亏[{total_percentage_red:.2f}%]  绿色盈亏[{total_percentage_green:.2f}%  黑色盈亏[{total_percentage_black:.2f}%]")


    # 在右键单击事件处理函数中，获取当前光标下的项，并设置为选中项，然后显示右键菜单。
    def show_menu(self, event):
        item = self.stock_tree.identify_row(event.y)
        if item:
            self.stock_tree.selection_set(item)
            self.menu.post(event.x_root, event.y_root)


    # 定义获取选中项数据的处理函数，从Treeview中获取选中项的标识符，并提取其数据。
    # 访问 东方财富网网站页面
    def get_www_eastmoney_com(self):
        selected_item = self.stock_tree.selection()[0]
        item_data = self.stock_tree.item(selected_item, "values")
        # print("Selected Item Data:", item_data)
        stock_code = item_data[0]  # 提取股票代码
        url = f"https://data.eastmoney.com/stockcomment/stock/{stock_code}.html"
        # print("Opening URL:", url)
        # https://data.eastmoney.com/stockcomment/stock/601618.html
        # 打开网页
        webbrowser.open(url)

    # 访问 同花顺财经网站页面
    def get_www_0033_com(self):
        selected_item = self.stock_tree.selection()[0]
        item_data = self.stock_tree.item(selected_item, "values")
        # print("Selected Item Data:", item_data)
        stock_code = item_data[0]  # 提取股票代码
        url = f"https://stockpage.10jqka.com.cn/{stock_code}/"
        # print("Opening URL:", url)
        # https://stockpage.10jqka.com.cn/600508/
        # 打开网页
        webbrowser.open(url)


    def Analyze_stock_data_menu(self):
        selected_item = self.stock_tree.selection()[0]
        item_data = self.stock_tree.item(selected_item, "values")
        # print("Selected Item Data:", item_data)
        stock_code = item_data[0]  # 提取股票代码
        stock_name = item_data[1]  # 提取股票名称
        print(f"Analyze_stock_data_menu:{stock_code}  {stock_name}")
        self.Analyze_individual_stock_data(stock_code, stock_name)


    # 在后台线程中打开股票列表配置文件
    def edit_stock_config_list(self):
        selected_file = self.get_selected_file()
        # 在后台线程中打开文件
        threading.Thread(target=self.open_file_in_default_application, args=(selected_file,), daemon=True).start()

    def check_switch_state(self):
        if self.switch_var.get():
            print("分析全部历史数据：开关已打开")
        else:
            print("分析全部历史数据：开关已关闭")

    def on_closing(self):
        # 在主程序退出时设置停止事件，等待线程完成
        self.stop_event.set()
        
        # 清理资源
        try:
            if hasattr(self, 'resources'):
                for resource_name, resource in self.resources.items():
                    try:
                        if hasattr(resource, 'terminate'):
                            resource.terminate()
                        elif hasattr(resource, 'close'):
                            resource.close()
                        logging.info(f"已清理资源: {resource_name}")
                    except Exception as e:
                        logging.error(f"清理资源 {resource_name} 时出错: {e}")
        except Exception as e:
            logging.error(f"关闭程序时出错: {e}")
        
        self.root.destroy()

    def load_table_files(self):
        # 获取当前目录
        directory = CURRENT_DIR
        # 输出 Table.txt 文件的完整路径
        table_file_path = os.path.join(directory, "Table.txt")
        print(f"Table.txt 文件的完整路径: {table_file_path}")  # 添加这一行
        # 获取所有以 Table 开头的 TXT 文件
        files = [f for f in os.listdir(directory) if f.startswith("Table") and f.endswith(".txt")]
        self.Table_menu['values'] = files

    def update_status(self, status_text):
        """更新状态显示"""
        # 在主线程中更新UI
        if threading.current_thread() is threading.main_thread():
            self.get_stock_data_button.config(text=status_text)
        else:
            # 如果在子线程中，使用after方法在主线程中更新
            self.root.after(0, lambda: self.get_stock_data_button.config(text=status_text))


    def get_selected_file(self):
        # 从配置文件中获取文件名
        selected_file = self.load_from_config()  # 从配置文件中获取文件名
        if selected_file and os.path.exists(selected_file):
            # 检查文件是否在 Table_menu 中
            if selected_file in self.Table_menu['values']:
                self.Table_var.set(selected_file)  # 设置 Table_menu 的值
            return selected_file
        return self.Table_menu.get()  # 如果配置文件中没有有效文件名，则返回当前选择的文件名



    def check_stock_input(self, input_value):
        # 获取 historical_data 目录下最新日期的子目录
        base_path = os.path.join(CURRENT_DIR, 'historical_data')
        today = datetime.today()
        nearest_directory, _ = self.find_nearest_date_directories(base_path, today)
        if not nearest_directory:
            print("未找到最新的历史数据目录")
            return None
        latest_dir = os.path.join(base_path, nearest_directory)

        # 获取所有 json 文件
        json_files = [f for f in os.listdir(latest_dir) if f.endswith('.json')]
        # 直接用6位数字查找
        if re.fullmatch(r'\d{6}', input_value):
            for f in json_files:
                if input_value in f:
                    print(f"股票代码 '{input_value}' 确认正确")
                    return input_value
            print(f"股票代码 '{input_value}' 不存在")
            return None
        else:
            # 用名称查找
            for f in json_files:
                file_path = os.path.join(latest_dir, f)
                try:
                    with open(file_path, 'r', encoding='utf-8') as jf:
                        data = json.load(jf)
                        name = data['data'].get('name', '')
                        code = data['data'].get('code', '')
                        if input_value == name:
                            print(f"股票名称 '{input_value}' 对应的股票代码是: {code}")
                            return code
                except Exception as e:
                    continue
            print(f"未找到股票名称 '{input_value}' 对应的股票代码")
            return None


    def on_plot_button_click(self):
        textBoxValue = self.stock_id_entry.get()
        print(f"输入框内容: {textBoxValue}")
        stock_id = self.check_stock_input(textBoxValue)
        print(f"check_stock_input 返回: {stock_id}")
        if not stock_id:
            tk.messagebox.showerror("错误", f"未找到股票代码或名称: {textBoxValue}")
            return
        self.plot_stock_chart(stock_id)


    def on_get_stock_data_click(self):
        # 启动后台线程来运行长时间的操作
        self.get_stock_data_button.config(state='disabled', text='开始下载股票数据...')
        self.stop_event.clear()  # 确保停止事件被清除
        threading.Thread(target=self.run_get_stock_data_json, daemon=True).start()


    def One_click_analysis_of_stock_selection(self):
        # 启动后台线程来运行长时间的操作
        self.autoplot_button.config(state='disabled', text='②一键分析下列自选股(运行中)')
        threading.Thread(target=self.update_all_gp_stocks_and_analyze, daemon=True).start()

    def update_all_gp_stocks_and_analyze(self):
        try:
            # 先下载当前选中列表中的股票数据，而不是所有股票
            self.download_selected_gp_stocks_data()
            
            # 更新所有股票的收盘价和涨跌幅
            self.update_all_gp_stocks_price_and_change()
            
            # 执行原有的分析操作
            self.autoplot_button_click()
        except Exception as e:
            error_msg = f"分析过程中发生错误: {e}"
            print(error_msg)
            logging.error(error_msg)
        finally:
            # 确保按钮状态恢复
            self.autoplot_button.config(state='normal', text='②一键分析下列自选股')

    def download_selected_gp_stocks_data(self):
        """只下载当前选中列表文件中的股票数据"""
        # 获取当前选中的列表文件
        selected_file = self.get_selected_file()
        print(f"开始下载 {selected_file} 中的股票数据")
        logging.info(f"开始下载 {selected_file} 中的股票数据")
        
        # 获取最后交易的日期
        try:
            Stock_date_json = self.http_get_stock_data(f"0.000001")
            if Stock_date_json is None:
                error_msg = "获取股票数据失败，无法继续后续操作。"
                print(error_msg)
                logging.error(error_msg)
                return
            
            json_last_date = self.extract_last_date(Stock_date_json)
            Stock_date_time = json_last_date
            directory = os.path.join(CURRENT_DIR, f"historical_data/{Stock_date_time}")
            
            # 确保目录存在
            os.makedirs(directory, exist_ok=True)
        except Exception as e:
            error_msg = f"获取交易日期时出错: {e}"
            print(error_msg)
            logging.error(error_msg)
            return
        
        # 读取选中的列表文件中的股票
        file_path = os.path.join(CURRENT_DIR, selected_file)
        gp_stocks_to_download = []
        
        try:
            with open(file_path, 'r', encoding='gbk') as file:
                for line in file:
                    parts = line.strip().split('\t')
                    if len(parts) >= 2:
                        stock_code = parts[0][-6:]  # 获取股票代码
                        stock_name = parts[1]       # 获取股票名称
                        gp_stocks_to_download.append((stock_code, stock_name))
        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {e}")
            return
        
        total_gp_stocks = len(gp_stocks_to_download)
        completed_count = 0
        
        def update_progress():
            nonlocal completed_count
            progress = (completed_count / total_gp_stocks) * 100
            self.update_status(f"下载中... {completed_count}/{total_gp_stocks} ({progress:.1f}%)")
        
        # 优化线程池配置
        max_workers = min(10, os.cpu_count() * 2)  # 根据CPU核心数动态调整
        retry_limit = 3  # 添加重试机制
        
        def process_stock(stock_info):
            nonlocal completed_count
            stock_code, stock_name = stock_info
            
            for retry in range(retry_limit):
                try:
                    print(f"正在获取股票 {stock_code} [{stock_name}] 的数据... (尝试 {retry+1}/{retry_limit})")
                    
                    # 尝试不同的市场前缀获取数据
                    data = None
                    market_code = None
                    
                    for market in ["0", "1"]:  # 0为深市，1为沪市
                        data = self.http_get_stock_data(f"{market}.{stock_code}")
                        if data:
                            market_code = market
                            break
                    
                    if data:
                        # 根据market确定前缀
                        prefix = "SZ" if market_code == "0" else "SH"
                        file_name = f"{prefix}{stock_code}"
                        
                        # 检查文件是否已存在
                        json_file_path = os.path.join(directory, f"{file_name}.json")
                        if os.path.exists(json_file_path):
                            print(f"股票 {stock_code} [{stock_name}] 的数据文件已存在")
                            break  # 文件存在，跳出重试循环
                        
                        # 保存数据到文件
                        json_file_path = self.save_data_to_file(data, file_name, directory)
                        print(f"成功下载股票 {stock_code} [{stock_name}] 的数据")
                        break  # 成功下载，跳出重试循环
                    else:
                        if retry < retry_limit - 1:  # 如果不是最后一次尝试
                            print(f"股票 {stock_code} [{stock_name}] 获取失败，将重试...")
                            time.sleep(1)  # 重试前等待
                        else:
                            print(f"股票 {stock_code} [{stock_name}] 的数据获取失败，已达到最大重试次数")
                
                except Exception as e:
                    if retry < retry_limit - 1:  # 如果不是最后一次尝试
                        print(f"股票 {stock_code} [{stock_name}] 处理出错，将重试: {e}")
                        time.sleep(1)  # 重试前等待
                    else:
                        print(f"股票 {stock_code} [{stock_name}] 处理时发生错误，已达到最大重试次数: {e}")
            
            # 无论成功与否，都增加计数并更新进度
            completed_count += 1
            update_progress()
        
        # 使用线程池并行处理，动态调整线程数
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(process_stock, stock): stock for stock in gp_stocks_to_download}
            
            for future in concurrent.futures.as_completed(futures):
                stock = futures[future]
                try:
                    future.result()
                except Exception as e:
                    print(f"Error processing {stock[0]}: {e}")
                    logging.error(f"Error processing {stock[0]}: {e}")
        
        self.update_status(f"下载完成 {completed_count}/{total_gp_stocks}")
        print(f"已完成 {selected_file} 中所有股票数据的下载")

    def update_all_gp_stocks_price_and_change(self):
        # 使用类中已定义的数据库连接
        cursor = self.cursor

        try:
            # 获取所有股票代码
            cursor.execute("SELECT code, name, market FROM gp_stocks")
            gp_stocks = cursor.fetchall()

            for stock_code, stock_name, market in gp_stocks:
                # 获取最新的股票数据
                file_path = self.find_stock_file(stock_code, stock_name)
                if file_path:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        json_data = json.load(file)
                    
                    klines = json_data['data']['klines']
                    if klines:
                        latest_kline = klines[-1].split(',')
                        close_price = float(latest_kline[2])
                        change_percentage = float(latest_kline[8])

                        # 更新数据库
                        self.update_stock_price_and_change(stock_code, market, close_price, change_percentage)
                else:
                    print(f"未找到股票 {stock_code} {stock_name} 的数据文件")

        except Exception as e:
            print(f"更新股票数据时发生错误: {e}")
        finally:
            conn.close()

        print("所有股票的收盘价和涨跌幅更新完成")

    def run_get_stock_data_json(self, max_workers=10):
        # 抓取A股所有股票的收盘数据，max_workers=10 表示启用的线程数
        self.a_all_date()
        # 连接数据库
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()
        # 查询股票代码
        cursor.execute("SELECT code,name,market FROM gp_stocks")
        gp_stocks = cursor.fetchall()
        total_gp_stocks = len(gp_stocks)
        completed_count = 0  # 计数器
        skipped_count = 0  # 跳过的计数器

        # 创建线程安全的计数器和队列
        thread_progress = {}  # 存储每个线程的进度 {thread_id: {'current': 0, 'total': 0, 'processed': 0, 'skipped': 0}}
        thread_assignments = {}  # 存储每个线程分配的任务 {thread_id: [file_paths]}
        thread_lock = threading.Lock()
        task_queues = {}  # 为每个线程创建独立的队列

        # 获取最后交易的日期
        Stock_date_json = self.http_get_stock_data(f"0.000001")
        if Stock_date_json is None:
            print("获取股票数据失败，无法继续后续操作。")
            return
        json_last_date = self.extract_last_date(Stock_date_json)
        # 转换日期格式为 YYYYMMDD
        Stock_date_time = json_last_date
        # 使用全局变量 CURRENT_DIR 构建目录路径
        directory = os.path.join(CURRENT_DIR, f"historical_data/{Stock_date_time}")

        print(directory)
        time.sleep(1)  # 等待1秒

        # 更新下载按钮的文本
        self.get_stock_data_button.config(text='下载中... 0%')

        # 加载分析记录配置文件
        analysis_records = self.load_analysis_records()

        def update_progress():
            progress = (completed_count + skipped_count) / total_gp_stocks * 100  # 计算总进度
            self.get_stock_data_button.config(text=f'下载中... {progress:.2f}%')  # 更新按钮文本

        # 第一阶段：单线程下载所有股票数据
        def download_stock_data(stock):
            nonlocal completed_count, skipped_count
            try:
                stock_code = stock[0]
                stock_name = stock[1]
                market = stock[2]
                
                if market == "0":
                    prefix = "SZ"
                elif market == "1":
                    prefix = "SH"
                else:
                    prefix = "SZ"
                prefix_secid = f"{prefix}{stock_code}"

                print(f"正在获取股票 {stock_code} [{stock_name}] 的数据...")
                
                json_file_path = os.path.join(directory, f"{prefix_secid}.json")
                if os.path.exists(json_file_path):
                    print(f"股票 {stock_code} [{stock_name}] 的数据文件已存在")
                    skipped_count += 1
                    update_progress()
                    return json_file_path

                if self.stop_event.is_set():
                    print("任务被中断")
                    skipped_count += 1
                    update_progress()
                    return None

                # 根据前缀执行不同操作
                if market in ["0", "1"]:
                    data = self.http_get_stock_data(f"{market}.{stock_code}")
                    if data:
                        json_file_path = self.save_data_to_file(data, prefix_secid, directory)
                        completed_count += 1
                        update_progress()
                        time.sleep(1)  # 单线程下载，增加延时避免被风控
                        return json_file_path
                else:
                    # 尝试两个接口
                    data = self.http_get_stock_data(f"{market}.{stock_code}") or self.http_get_stock_data(f"1.{stock_code}")
                    if data:
                        json_file_path = self.save_data_to_file(data, prefix_secid, directory)
                        completed_count += 1
                        update_progress()
                        time.sleep(1)  # 单线程下载，增加延时避免被风控
                        return json_file_path

                print(f"股票代码 {prefix_secid} 的数据为空，跳过处理")
                skipped_count += 1
                update_progress()
                return None

            except Exception as e:
                print(f"股票 {prefix_secid} 处理时发生错误: {e}")
                skipped_count += 1
                update_progress()
                return None


        # 第二阶段：使用线程池并行分析已下载的数据
        def worker(thread_id):
            # 获取该线程分配的任务列表
            assigned_files = thread_assignments.get(thread_id, [])
            thread_total = len(assigned_files)

            # 初始化线程进度
            with thread_lock:
                thread_progress[thread_id] = {
                    'current': 0,
                    'total': thread_total,
                    'processed': 0,
                    'skipped': 0
                }

            for i, json_file_path in enumerate(assigned_files):
                if self.stop_event.is_set():
                    break

                if json_file_path and os.path.exists(json_file_path):
                    try:
                        stock_code = os.path.basename(json_file_path).replace('.json', '')

                        # 检查该文件是否已经在当前目录被分析过
                        file_name = os.path.basename(json_file_path)
                        dir_name = os.path.basename(os.path.dirname(json_file_path))

                        # 更新当前处理的文件索引
                        with thread_lock:
                            thread_progress[thread_id]['current'] = i + 1

                        # 如果文件已经在当前目录被分析过，则跳过
                        if file_name in analysis_records and analysis_records[file_name] == dir_name:
                            with thread_lock:
                                thread_progress[thread_id]['skipped'] += 1
                                current = thread_progress[thread_id]['current']
                                total = thread_progress[thread_id]['total']
                                print(f"线程{thread_id}:[{current}/{total}] 文件 {file_name} 已分析过，跳过")
                            continue

                        # 显示正在处理的文件
                        with thread_lock:
                            current = thread_progress[thread_id]['current']
                            total = thread_progress[thread_id]['total']
                            print(f"线程{thread_id}:[{current}/{total}] 正在分析 {stock_code}")

                        # 分析文件
                        self.autoplot_stock_data(json_file_path, "全部趋势")

                        # 更新处理计数和分析记录
                        with thread_lock:
                            thread_progress[thread_id]['processed'] += 1
                            analysis_records[file_name] = dir_name
                            # 每分析10个文件保存一次配置，避免频繁IO
                            if thread_progress[thread_id]['processed'] % 10 == 0:
                                self.save_analysis_records(analysis_records)

                    except Exception as e:
                        print(f"线程{thread_id}: 分析文件 {json_file_path} 时出错: {e}")
                        with thread_lock:
                            thread_progress[thread_id]['skipped'] += 1

            # 线程完成后显示统计信息
            with thread_lock:
                progress = thread_progress[thread_id]
                print(f"线程{thread_id} 完成: 总计{progress['total']}个文件, 处理{progress['processed']}个, 跳过{progress['skipped']}个")


        # 执行第一阶段：单线程下载
        print("\n开始第一阶段：单线程下载股票数据...")
        downloaded_files = []
        for stock in gp_stocks:
            if self.stop_event.is_set():
                break
            json_file_path = download_stock_data(stock)
            if json_file_path:
                downloaded_files.append(json_file_path)

        # 执行第二阶段：多线程分析
        if not self.stop_event.is_set():
            print("\n开始第二阶段：多线程分析股票数据...")
            threads = []

            # 计算每个线程应处理的文件数量并预先分配任务
            total_files = len(downloaded_files)
            base_files_per_thread = total_files // max_workers
            extra_files = total_files % max_workers

            print(f"共有{total_files}个文件需要处理，将分配给{max_workers}个线程")

            # 为每个线程预先分配任务
            current_index = 0
            for thread_id in range(1, max_workers + 1):
                # 计算该线程应处理的文件数量
                thread_file_count = base_files_per_thread + (1 if thread_id <= extra_files else 0)

                # 分配文件给该线程
                thread_files = downloaded_files[current_index:current_index + thread_file_count]
                thread_assignments[thread_id] = thread_files
                current_index += thread_file_count

                print(f"线程{thread_id}: 分配{len(thread_files)}个文件")

            # 创建并启动线程
            for thread_id in range(1, max_workers + 1):
                if thread_assignments[thread_id]:  # 只启动有任务的线程
                    t = threading.Thread(target=worker, args=(thread_id,))
                    t.daemon = True
                    t.start()
                    threads.append(t)
                    print(f"线程{thread_id}已启动")

            # 等待所有线程结束并显示完成信息
            for i, t in enumerate(threads):
                t.join()

            print("\n所有线程已完成数据分析工作")

            # 显示最终统计信息
            total_processed = 0
            total_skipped = 0
            for thread_id, progress in thread_progress.items():
                total_processed += progress['processed']
                total_skipped += progress['skipped']
                print(f"线程{thread_id}最终统计: 处理{progress['processed']}个, 跳过{progress['skipped']}个")

            print(f"总计: 处理{total_processed}个文件, 跳过{total_skipped}个文件")

            # 保存最终的分析记录
            self.save_analysis_records(analysis_records)


        conn.close()
        self.get_stock_data_button.config(state='normal', text='①下载股票数据完成')
        print("=" * 50)
        print("\n\n所有股票数据已经获取和分析完成。")
        print("=" * 50)


    def on_enter_key_pressed(self, event):
        textBoxValue = self.stock_id_var.get()
        stock_id = self.check_stock_input(textBoxValue)
        print(f"check_stock_input 返回: {stock_id}")
        if not stock_id:
            from tkinter import messagebox
            messagebox.showerror("错误", f"未找到股票代码或名称: {textBoxValue}")
            return
        self.plot_stock_chart(stock_id)


    def analyze_stock_klines(self,  klines, recent_days=5):
        # 解析数据
        dates = []
        opens = []
        closes = []
        highs = []
        lows = []
        changes = []
        today_changes = []

        for kline in klines:
            parts = kline.split(',')
            dates.append(parts[0])
            opens.append(float(parts[1]))  # 开盘价信息
            closes.append(float(parts[2]))  # 收盘价信息
            highs.append(float(parts[3]))  # 最高价信息
            lows.append(float(parts[4]))  # 最低价信息

            # 计算单日涨跌幅度
            change = round((closes[-1] - opens[-1]) / opens[-1] * 100, 2)
            changes.append(change)

            # 今日涨跌
            today_change = '涨' if change > 0 else '跌'
            today_changes.append(today_change)

        # 创建DataFrame
        df = pd.DataFrame({
            '日期': pd.to_datetime(dates),  # 将日期文本转换为日期时间格式
            '开盘价': opens,
            '收盘价': closes,
            '最高价': highs,
            '最低价': lows,
            '涨跌幅(%)': changes,
            '今日涨跌': today_changes
        })

        # 将日期设置为索引
        df.set_index('日期', inplace=True)

        # 计算SAR指标
        df['SAR'] = talib.SAR(df['最高价'], df['最低价'], acceleration=0.02, maximum=0.2)

        # 判断SAR指标颜色
        df['SAR颜色'] = ['红' if close > sar else '绿' for close, sar in zip(df['收盘价'], df['SAR'])]

        # 判断最新一天的SAR指标颜色
        latest_sar_value = df['SAR'].iloc[-1]
        latest_close_price = df['收盘价'].iloc[-1]
        latest_sar_color = '红' if latest_close_price > latest_sar_value else '绿'

        # 判断最新的两天日期SAR指标是否都为红色
        latest_two_days_sar_color = all(df['收盘价'].iloc[-2:] > df['SAR'].iloc[-2:])

        # 计算最近5日的涨跌情况及比例
        recent_data = df.tail(recent_days)
        total_up_days = recent_data[recent_data['涨跌幅(%)'] > 0].shape[0]
        total_down_days = recent_data[recent_data['涨跌幅(%)'] < 0].shape[0]
        up_ratio = round(total_up_days / recent_days * 100, 2)
        down_ratio = round(total_down_days / recent_days * 100, 2)

        # 计算最近5日的综合涨跌幅度百分比
        total_change = round(recent_data['涨跌幅(%)'].sum(), 2)

        # 准备打印信息
        print_info = []
        print_info.append("最近5日的开盘价、收盘价、单日涨跌幅度和今日涨跌情况：")
        for index, row in recent_data.iterrows():
            print_info.append(
                f"{index.date()}  {row['开盘价']}  {row['收盘价']}    {row['涨跌幅(%)']}    {row['今日涨跌']}    {row['SAR']:.4f}    {row['SAR颜色']}")

        print_info.append(f"\n最近5日内总计涨天数: {total_up_days} 天，跌天数: {total_down_days} 天")
        print_info.append(f"最近5日内涨跌比例: 涨 {up_ratio:.2f}% ，跌 {down_ratio:.2f}%")
        print_info.append(f"最近5日内综合涨跌幅度: {total_change:.2f}%")
        print_info.append(f"最新一天的SAR指标值: {latest_sar_value:.2f}，颜色: {latest_sar_color}")
        print_info.append(f"最新两天的SAR指标是否都为红色: {'是' if latest_two_days_sar_color else '否'}")
        # 返回结果
        return up_ratio, down_ratio, total_change, latest_sar_value, latest_sar_color, latest_two_days_sar_color, latest_close_price, "\n".join(
            print_info)



    # 更新数据库 gp_stocks表内 close_price(收盘价),change_percentage(涨跌幅)
    def update_stock_price_and_change(self, stock_code, market, close_price, change_percentage):
        if not stock_code or market is None or close_price is None or change_percentage is None:
            print(
                f"update_stock_price_and_change: 输入参数不能为空: [code={stock_code}] [market={market}] [close_price={close_price}] [change_percentage={change_percentage}]")
            return

        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()

        # 更新收盘价和涨跌幅
        cursor.execute('''
        UPDATE gp_stocks
        SET close_price = %s, change_percentage = %s
        WHERE code = %s AND market = %s
        ''', (close_price, change_percentage, stock_code, market))

        conn.commit()
        conn.close()

    # 向数据库插入技术指标的决策
    def add_indicator(self, stock_code, market, date, indicator_name, indicator_value, DB_FILE='all_stocker_data.db'):
        # 检查传入的参数是否为空
        if (stock_code is None or stock_code.strip() == '' or
                market is None or (isinstance(market, str) and market.strip() == '') or
                date is None or date.strip() == '' or
                indicator_name is None or indicator_name.strip() == '' or
                indicator_value is None or indicator_value.strip() == ''):
            return

        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()

        try:
            # 查找股票的 ID
            cursor.execute('''
            SELECT id FROM gp_stocks WHERE code = %s AND market = %s
            ''', (stock_code, market))
            stock_id = cursor.fetchone()

            if stock_id:
                stock_id = stock_id[0]

                # 检查是否已经存在相同的记录
                cursor.execute('''
                SELECT 1 FROM gp_indicators WHERE stock_id = %s AND date = %s AND indicator_name = %s
                ''', (stock_id, date, indicator_name))

                if cursor.fetchone():
                    # 如果记录存在，则更新
                    cursor.execute('''
                    UPDATE gp_indicators 
                    SET indicator_value = %s
                    WHERE stock_id = %s AND date = %s AND indicator_name = %s
                    ''', (indicator_value, stock_id, date, indicator_name))
                else:
                    # 如果记录不存在，则插入
                    cursor.execute('''
                    INSERT INTO gp_indicators (stock_id, date, indicator_name, indicator_value)
                    VALUES (%s, %s, %s, %s)''', (stock_id, date, indicator_name, indicator_value))
            else:
                print(f'add_indicator: 未找到股票 {stock_code} ({market}) 的记录，无法插入或更新技术指标数据。')

            conn.commit()
        except sqlite3.Error as e:
            print(f"数据库操作出错: {e}")
            conn.rollback()
        finally:
            conn.close()


    # 通过数据库 获取 SAR,DPO 技术指标的决策
    def query_indicator(self, stock_code, market, date, indicator_name, DB_FILE = "all_stocker_data.db"):
        print(f"query_indicator: stock_code={stock_code}, market={market}, date={date}, indicator_name={indicator_name}, DB_FILE={DB_FILE}")
        
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT i.indicator_value FROM gp_indicators i
        JOIN gp_stocks s ON i.stock_id = s.id
        WHERE s.code = %s AND s.market = %s AND i.date = %s AND i.indicator_name = %s
        ''', (stock_code, market, date, indicator_name))

        result = cursor.fetchone()

        conn.close()

        if result:
            return result[0]
        else:
            return None


    def query_indicator_piliang(self, stock_code, market, date, indicator_names=['DPO', 'SAR']):
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()

        placeholders = ', '.join('%s' for _ in indicator_names)
        query = f'''
        SELECT i.indicator_name, i.indicator_value FROM gp_indicators i
        JOIN gp_stocks s ON i.stock_id = s.id
        WHERE s.code = %s AND s.market = %s AND i.date = %s AND i.indicator_name IN ({placeholders})
        '''
        cursor.execute(query, (stock_code, market, date, *indicator_names))

        results = cursor.fetchall()

        conn.close()

        if results:
            return {result[0]: result[1] for result in results}
        else:
            return None


    def query_indicators_batch(self, stock_code, stock_market, dates, indicator_name):

        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()

        # 创建占位符字符串
        placeholders = ', '.join('?' for _ in dates)

        # 修改查询语句以支持批量查询
        query = f'''
        SELECT date, indicator_value 
        FROM gp_indicators 
        WHERE stock_id = (SELECT id FROM gp_stocks WHERE code = ? AND market = ?) 
        AND indicator_name = ? 
        AND date IN ({placeholders})
        ORDER BY date DESC
        '''

        # 执行查询
        cursor.execute(query, (stock_code, stock_market, indicator_name, *dates))
        results = cursor.fetchall()

        conn.close()
        return results




    def calculate_dpo_from_json(self, json_file, dpo_period=20, madpo_period=6, trend_days=[5, 10]):
        """
        从JSON文件中读取股票历史价格数据，并计算DPO和MADPO。

        参数：
        json_file: JSON文件路径

        madpo_period: MADPO计算的周期（一般为6）
        trend_days: 用于走势判断的天数列表

        返回：
        DataFrame，包含原始数据以及计算出的DPO和MADPO
        """
        # 读取JSON文件
        with open(json_file, 'r', encoding='utf-8') as file:
            json_data = json.load(file)

        # 提取股票代码和名称
        stock_code = json_data['data']['code']
        stock_name = json_data['data']['name']
        klines_all = json_data['data']['klines']

        # 创建DataFrame
        df = pd.DataFrame([line.split(',') for line in klines_all], columns=[
            'Date', 'Open', 'Close', 'High', 'Low', 'Volume', 'Turnover',
            'Amplitude', 'Change', 'ChangePercent', 'TurnoverRate'
        ])

        # 转换数据类型
        df['Date'] = pd.to_datetime(df['Date'])
        df[['Open', 'Close', 'High', 'Low', 'Volume']] = df[['Open', 'Close', 'High', 'Low', 'Volume']].astype(float)

        # 计算DPO
        df['SMA'] = talib.SMA(df['Close'], timeperiod=dpo_period)
        df['DPO'] = df['Close'] - df['SMA'].shift(int(dpo_period / 2 + 1))

        # 计算MADPO
        df['MADPO'] = talib.SMA(df['DPO'], timeperiod=madpo_period)

        # 获取最近指定天数的DPO和MADPO数据
        result = {}
        for days in trend_days:
            df_last_days = df.tail(days)

            # 分析DPO数据的后市走势
            dpo_values = df_last_days['DPO'].tolist()
            madpo_values = df_last_days['MADPO'].tolist()

            if all(value > 0 for value in dpo_values):
                trend = "上升趋势"
            elif all(value < 0 for value in dpo_values):
                trend = "下降趋势"
            else:
                trend = "震荡市场"

            result[f'{days}天趋势'] = trend
            result[f'{days}天数据'] = df_last_days[['Date', 'DPO', 'MADPO']]

            # 判断买点和卖点
            signals = []

            for i in range(1, len(df_last_days)):
                if df_last_days.iloc[i]['DPO'] > df_last_days.iloc[i]['MADPO'] and \
                        df_last_days.iloc[i - 1]['DPO'] <= df_last_days.iloc[i - 1]['MADPO']:
                    signals.append((df_last_days.iloc[i]['Date'], '红'))
                elif df_last_days.iloc[i]['DPO'] < df_last_days.iloc[i]['MADPO'] and \
                        df_last_days.iloc[i - 1]['DPO'] >= df_last_days.iloc[i - 1]['MADPO']:
                    signals.append((df_last_days.iloc[i]['Date'], '绿'))

            # 按日期排序买卖点信号
            signals.sort()

            # 将买卖点信号加入结果
            signals_output = "\n".join([f"{date.strftime('%Y-%m-%d')} {signal}" for date, signal in signals])
            result[f'{days}天买卖点'] = signals_output

            # 判断强弱势
            if df_last_days.iloc[-1]['DPO'] > df_last_days.iloc[-1]['MADPO']:
                strength = "强势"
            else:
                strength = "弱势"

            result[f'{days}天强弱势'] = strength


        last_signal = None
        for days in trend_days:
            if f'{days}天买卖点' in result:
                signals = result[f'{days}天买卖点'].split("\n")
                if signals and signals[-1].split():  # 确保split()的结果非空
                    parts = signals[-1].split()
                    if len(parts) > 1:  # 确保至少有两个元素
                        last_signal = parts[1]
                        result['最后的买入或卖出决策'] = last_signal
                        break  # 如果你只想处理第一个匹配的days
                    else:
                        # 处理parts长度不足的情况（可选）
                        last_signal = None
                else:
                    # 处理signals为空的情况（可选）
                    last_signal = None

        dpo_value = df_last_days.iloc[-1]['DPO'].round(decimals=4)

        # 返回结果
        return df, last_signal, dpo_value



    def open_file_in_default_application(self, file_path):
        """在默认应用程序中打开文件"""
        if platform.system() == "Windows":
            subprocess.run(['notepad', file_path], check=True)
        elif platform.system() == "Darwin":  # macOS
            subprocess.run(['open', file_path], check=True)
        elif platform.system() == "Linux":
            subprocess.run(['xdg-open', file_path], check=True)
        else:
            print("当前操作系统不支持自动打开文件的功能。")


    def load_analysis_records(self):
        """加载分析记录配置文件,增加验证和错误处理"""
        config_path = os.path.join(CURRENT_DIR, "analysis_records.ini")
        backup_path = os.path.join(CURRENT_DIR, "analysis_records.bak")
        records = {}
        
        try:
            # 首先尝试加载主配置文件
            if os.path.exists(config_path):
                records = self._load_records_file(config_path)
                if records:
                    # 成功加载后创建备份
                    self._backup_records(config_path, backup_path)
            
            # 如果主文件加载失败,尝试加载备份
            if not records and os.path.exists(backup_path):
                logging.warning("主配置文件加载失败,尝试加载备份文件")
                records = self._load_records_file(backup_path)
                if records:
                    # 从备份恢复主文件
                    shutil.copy2(backup_path, config_path)
            
            # 验证加载的记录
            records = self._validate_records(records)
            
            logging.info(f"成功加载 {len(records)} 条有效分析记录")
            return records
            
        except Exception as e:
            logging.error(f"加载分析记录时发生错误: {e}")
            return {}

    def _load_records_file(self, file_path):
        """从文件加载记录的辅助方法"""
        records = {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        try:
                            key, value = line.split('=', 1)
                            records[key.strip()] = value.strip()
                        except ValueError:
                            logging.warning(f"忽略无效行: {line}")
                            continue
            return records
        except Exception as e:
            logging.error(f"读取文件 {file_path} 失败: {e}")
            return {}

    def _validate_records(self, records):
        """验证记录的有效性"""
        valid_records = {}
        for file_name, dir_name in records.items():
            # 验证文件名格式
            if not file_name.endswith('.json'):
                logging.warning(f"忽略无效文件名: {file_name}")
                continue
                
            # 验证目录名格式(应为8位数字)
            if not re.match(r'^\d{8}$', dir_name):
                logging.warning(f"忽略无效目录名: {dir_name}")
                continue
                
            valid_records[file_name] = dir_name
        
        return valid_records


    def save_analysis_records(self, records):
        """保存分析记录配置文件,增加原子写入和备份"""
        config_path = os.path.join(CURRENT_DIR, "analysis_records.ini")
        temp_path = config_path + '.tmp'
        backup_path = os.path.join(CURRENT_DIR, "analysis_records.bak")
        
        try:
            # 先写入临时文件
            self._write_records_file(records, temp_path)
            
            # 验证临时文件
            test_records = self._load_records_file(temp_path)
            if len(test_records) != len(records):
                raise ValueError("临时文件验证失败")
                
            # 创建备份
            if os.path.exists(config_path):
                self._backup_records(config_path, backup_path)
                
            # 原子替换主文件
            os.replace(temp_path, config_path)
            
            logging.info(f"成功保存 {len(records)} 条分析记录")
            
        except Exception as e:
            logging.error(f"保存分析记录失败: {e}")
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise

    def _write_records_file(self, records, file_path):
        """写入记录到文件的辅助方法"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("# 股票JSON文件分析记录配置文件\n")
            f.write("# 格式: JSON文件名=分析它的目录名\n")
            f.write("# 此文件由程序自动生成与管理\n\n")
            
            # 按文件名排序写入
            for file_name, dir_name in sorted(records.items()):
                f.write(f"{file_name}={dir_name}\n")

    def _backup_records(self, source_path, backup_path):
        """创建配置文件的备份"""
        try:
            shutil.copy2(source_path, backup_path)
        except Exception as e:
            logging.error(f"创建配置文件备份失败: {e}")


    def save_analysis_records(self, records):
        """保存分析记录配置文件"""
        config_path = os.path.join(CURRENT_DIR, "analysis_records.ini")
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write("# 股票JSON文件分析记录配置文件\n")
                f.write("# 格式: JSON文件名=分析它的目录名\n")
                f.write("# 此文件由程序自动生成，请勿手动修改\n\n")
                
                for file_name, dir_name in sorted(records.items()):
                    f.write(f"{file_name}={dir_name}\n")
            print(f"已保存 {len(records)} 条分析记录")
            logging.info(f"已保存 {len(records)} 条分析记录")
        except Exception as e:
            print(f"保存分析记录配置文件出错: {e}")
            logging.error(f"保存分析记录配置文件出错: {e}")




    def autoplot_button_click(self):
        # 这个函数使用find_nearest_date_directories来查找最近的日期目录。
        # 构造完整的目录路径并检查其存在性。
        # 遍历该目录下的所有文件，如果文件名以.json结尾，则提取文件名的倒数六位数字并打
        # 使用全局变量 CURRENT_DIR 构建目录路径
        base_path = os.path.join(CURRENT_DIR, f"historical_data")
        target_date = datetime.today()
        nearest_directory, second_nearest_directory = self.find_nearest_date_directories(base_path, target_date)



        # 通过Table.txt 获取需要分析的股票
        file_path = os.path.join(CURRENT_DIR, f"Table.txt")
        if not os.path.exists(file_path):
            print("开始对所有数据进行分析：Table.txt 文件不存在")
            return

        # 首先计算总行数
        with open(file_path, 'r', encoding='gbk') as file:
            total_gp_stocks = sum(1 for line in file if len(line.strip().split('\t')) >= 2)

        # 打开指定路径的文件，以只读模式读取，编码方式为 gbk
        with open(file_path, 'r', encoding='gbk') as file:
            current_count = 0
            # 遍历文件的每一行
            for line in file:
                # 去除每行的首尾空格，并以制表符分割成多个部分
                parts = line.strip().split('\t')
                # 检查分割后的列表长度是否至少有2个元素
                if len(parts) >= 2:
                    current_count += 1
                    # 获取股票代码的右边 6 位数字作为股票代码
                    stock_code = parts[0][-6:]
                    stock_name = parts[1]
                    print(f"{current_count}/{total_gp_stocks} 分析股票：{stock_name} {stock_code}")
                    file_path = self.find_stock_file(stock_code, stock_name)

                    if file_path:
                        self.autoplot_stock_data(file_path)
                    else:
                        print("未找到对应的股票数据文件。")

            # 分析完毕所有的自选股票信息后，自动打开 上涨的txt
            # 获取当前目录
            directory = CURRENT_DIR
            file_to_open = os.path.join(directory, "买卖信号", f"自选趋势上涨{nearest_directory}.txt")

            # 在后台线程中打开文件
            threading.Thread(target=self.open_file_in_default_application, args=(file_to_open,), daemon=True).start()
            # 执行其他任务
            print("打开文件操作已启动，程序继续执行其他任务...")
            self.autoplot_button.config(state='normal', text='②一键分析下列自选股')


    # 分析单只股票的趋势策略
    def Analyze_individual_stock_data(self, stock_code, stock_name):
        file_path = self.find_stock_file(stock_code, stock_name)
        if file_path:
            self.autoplot_stock_data(file_path)
        else:
            print("Analyze_individual_stock_data: 未找到对应的股票数据文件。")



    def autoplot_stock_data(self, file_path, Output_log_filename="自选趋势"):
        # 6.13.5     /   12.23.10        /   20.46.15
        # 第一组MACD技术指标参数
        macd_1 = (6, 13, 5)
        macd_1_short, macd_1_long, macd_1_signal = macd_1
        # 第二组MACD技术指标参数
        macd_2 = (12, 23, 10)
        macd_2_short, macd_2_long, macd_2_signal = macd_2
        # 第三组MACD技术指标参数
        macd_3 = (15, 28, 13)
        macd_3_short, macd_3_long, macd_3_signal = macd_3

        # use_sliding_window = True      #表示全数据分析模式，分析所有历史数据
        # use_sliding_window = False      #表示最新一天数据模式，分析最新一天的数据
        # 使用界面上选中开关进行控制
        if self.switch_var.get():
            use_sliding_window = True
        else:
            use_sliding_window = False

        # print(f"file_path:{file_path}")
        with open(file_path, 'r', encoding='utf-8') as file:
            json_data = json.load(file)

        # 获取股票代码和名称
        stock_code = json_data['data']['code']
        stock_name = json_data['data']['name']
        stock_market = json_data['data']['market']
        stock_decimal = json_data['data']['decimal']
        stock_dktotal = json_data['data']['dktotal']
        stock_preKPrice = json_data['data']['preKPrice']
        klines_all = json_data['data']['klines']

        # 是否启用全部分析模式，还是每日数据分析模式做判断
        if not use_sliding_window:
            # 如果 use_sliding_window 是假（False），直接输出最后60行数据
            klines_all = klines_all[-60:]
            #print("如果 use_sliding_window 是假（False），直接输出最后60行数据")
        else:
            # 如果 use_sliding_window 是真（True），使用滑动窗口模式输出数据
            klines_all = klines_all[-150:]
            #print("如果 use_sliding_window 是真（True），全部数据逐一分析模式")

        # 从窗口界面上获取显示周期参数
        #period = int(self.period_var.get())
        period = 60     # 固定周期参数
        # 获取 klines 的总行数
        total_rows = len(klines_all)

        # 检查是否有足够的数据进行滑动窗口
        if total_rows >= period:
            # 从第 1 行开始到倒数第 60 行进行滑动窗口
            for i in range(total_rows - period + 1):
                klines = klines_all[i:i + period]
                #print(klines)

                # 获取最后一行的数据
                last_row = klines[-1]
                # 提取最后一行的日期字段
                last_date = last_row.split(',')[0]
                # 转换日期格式为 YYYYMMDD
                Stock_date_time = last_date.replace('-', '')
                # 输出最后一行的日期字段
                # print(f"最后一行的日期: {Stock_date_time}")

                dates = [entry.split(',')[0] for entry in klines]
                closing_prices = [float(entry.split(',')[2]) for entry in klines]       # 收盘价
                volumes = [float(entry.split(',')[5]) for entry in klines]
                change_percentage = [float(entry.split(',')[8]) for entry in klines]    # 涨跌幅

                # 记录最近3天是否绘制了买入和卖出信号的标志
                buy_signal_days = []
                sell_signal_days = []
                Macd_trading_index_number = 0

                # MACD 技术指标方法一
                macd_line, signal_line, macd_histogram = self.calculate_macd(closing_prices, macd_1_short, macd_1_long, macd_1_signal)
                Macd_trading_number = 0
                # 标注MACD的买卖点
                for i in range(1, len(macd_histogram)):
                    if macd_histogram[i] > 0 and macd_histogram[i - 1] < 0:
                        Macd_trading_number = 30  # MACD买卖指标数值
                    elif macd_histogram[i] < 0 and macd_histogram[i - 1] > 0:
                        Macd_trading_number = 0  # MACD买卖指标数值

                Macd_trading_index_number += Macd_trading_number  # macd 买卖指标数值

                # MACD 技术指标方法二

                macd_line, signal_line, macd_histogram = self.calculate_macd(closing_prices, macd_2_short, macd_2_long, macd_2_signal)
                Macd_trading_number = 0
                # 标注MACD的买卖点
                for i in range(1, len(macd_histogram)):
                    if macd_histogram[i] > 0 and macd_histogram[i - 1] < 0:
                        Macd_trading_number = 30  # MACD买卖指标数值
                    elif macd_histogram[i] < 0 and macd_histogram[i - 1] > 0:
                        Macd_trading_number = 0  # MACD买卖指标数值

                Macd_trading_index_number += Macd_trading_number  # macd 买卖指标数值

                # MACD 技术指标方法三
                macd_line, signal_line, macd_histogram = self.calculate_macd(closing_prices, macd_3_short, macd_3_long, macd_3_signal)
                Macd_trading_number = 0
                # 标注MACD的买卖点
                for i in range(1, len(macd_histogram)):
                    if macd_histogram[i] > 0 and macd_histogram[i - 1] < 0:
                        Macd_trading_number = 30  # MACD买卖指标数值
                    elif macd_histogram[i] < 0 and macd_histogram[i - 1] > 0:
                        Macd_trading_number = 0  # MACD买卖指标数值


                # 2024.07.07 增加对股票做DPO技术指标分析,检测最后是买入还是卖出策略
                df, dpo_last_signal, dpo_value = self.calculate_dpo_from_json(file_path, trend_days=[20])
                # 向数据库内添加DPO技术指标决策
                self.add_indicator(stock_code, stock_market, Stock_date_time, 'DPO', dpo_last_signal)


                # 获取最近5日的涨跌情况
                # up_ratio, down_ratio, total_change, print_info = self.analyze_stock_klines(klines_all)
                up_ratio, down_ratio, total_change, latest_sar_value, latest_sar_color, latest_two_days_sar_color, latest_close_price, print_info = self.analyze_stock_klines(
                    klines_all)
                # 向数据库内添加SAR技术指标决策
                self.add_indicator(stock_code, stock_market, Stock_date_time, 'SAR', latest_sar_color)

                stock_close_price, stock_change_percentage, stock_market, stock_color = self.get_stock_price_change_and_color(stock_code,stock_name)

                # stock_close_price, stock_change_percentage, stock_color = self.get_latest_change(stock_code, stock_name)
                # 更新数据库中股票的收盘价和涨跌幅
                self.update_stock_price_and_change(stock_code, stock_market, stock_close_price, stock_change_percentage)


                # -----------------------------------------------------
                Macd_trading_index_number += Macd_trading_number  # macd 买卖指标数值
                if Macd_trading_index_number > 80:

                    # 2024-08-01 增加统计股票预测准确率
                    json_file_path = file_path

                    prediction_data, accuracy, debug_output, last_signal_info, last_signal_count_info = self.analyze_stock_predictions(
                        json_file_path)

                    # 写出买卖日志记录到文档
                    # content = f'买入信号 {Macd_trading_index_number}%：{stock_name} [{stock_code}]\n----------------------------------------------------------------------------------'
                    # self.write_to_text(content, "买入信号", Stock_date_time)

                    # if up_ratio > down_ratio and latest_two_days_sar_color == True and last_signal == '红':
                    if latest_two_days_sar_color == True and dpo_last_signal == '红':
                        content2 = f'{stock_name} [{stock_code}]        SAR趋势:{latest_sar_color}        DPO趋势:{dpo_last_signal}     策略趋势:{last_signal_info}[{last_signal_count_info}]  \n{stock_name} [{stock_code}]        收盘价:{latest_close_price}       DPO值:{dpo_value}        涨跌比例:{up_ratio}/{down_ratio}        5日股价综合涨跌幅度:{total_change}\n----------------------------------------------------------------------------------'

                        self.write_to_text(content2, f"{Output_log_filename}上涨", Stock_date_time)

                    # 2024.06.24 增加将涨跌信息写入到数据库中
                    # 将 最终 趋势 信号(FTS)写入到数据库中
                    self.add_indicator(stock_code, stock_market, Stock_date_time, 'FTS', '涨')

                elif Macd_trading_index_number == 0:

                    # 写出买卖日志记录到文档
                    # content = f'卖出信号 {Macd_trading_index_number}%：{stock_name} [{stock_code}]\n-------------------------------------------------------------------'
                    # self.write_to_text(content, "卖出信号", Stock_date_time)

                    content2 = f'{stock_name} [{stock_code}]        SAR趋势:{latest_sar_color}        DPO趋势:{dpo_last_signal}     跌势  \n{stock_name} [{stock_code}]        收盘价:{latest_close_price}       DPO值:{dpo_value}        涨跌比例:{up_ratio}/{down_ratio}        5日股价综合涨跌幅度:{total_change}\n----------------------------------------------------------------------------------'
                    self.write_to_text(content2, f"{Output_log_filename}下跌", Stock_date_time)

                    # 2024.06.24 增加将涨跌信息写入到数据库中
                    # 将 最终 趋势 信号(FTS)写入到数据库中
                    self.add_indicator(stock_code, stock_market, Stock_date_time, 'FTS', '跌')

        else:
            print(f'{stock_name}[{stock_code}] 数据分析完毕！')


    def analyze_stock_predictions(self, json_file_path, days_to_analyze=60, days_to_check=2):
        # 读取JSON文件
        with open(json_file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        # 获取股票代码和名称
        stock_code = json_data['data']['code']
        stock_name = json_data['data']['name']
        stock_market = json_data['data']['market']

        # 获取股票日K线数据
        klines = json_data['data']['klines']
        kline_data = {}
        for kline in klines:
            parts = kline.split(',')
            date = parts[0].replace('-', '')
            open_price = float(parts[1])
            close_price = float(parts[2])
            change_percent = round((close_price - open_price) / open_price * 100, 2)
            change_status = '涨' if change_percent > 0 else '跌'
            kline_data[date] = {
                'open': open_price,
                'close': close_price,
                'change_percent': change_percent,
                'change_status': change_status
            }

        # 获取日期范围
        all_dates = sorted(kline_data.keys(), reverse=True)
        if len(all_dates) < days_to_analyze:
            print(f"可用的日期少于指定的分析天数 {days_to_analyze}")
            return None, None, None, None, None
        recent_dates = all_dates[:days_to_analyze]

        # 使用类中已定义的数据库连接
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()

        # 获取指定日期范围内的股票预测数据
        placeholders = ','.join(['%s'] * len(recent_dates))
        query = f'''
        SELECT date, indicator_value 
        FROM gp_indicators 
        WHERE stock_id = (SELECT id FROM gp_stocks WHERE code = %s AND market = %s) 
          AND indicator_name = 'FTS'
          AND date IN ({placeholders})
        ORDER BY date DESC
        '''
        cursor.execute(query, (stock_code, stock_market, *recent_dates))

        stock_predictions = cursor.fetchall()


        conn.close()

        # 检查是否有预测数据
        if not stock_predictions:
            print(f"Error: 数据库 gp_indicators 表中未找到 {stock_code} 的预测记录.")
            return None, None, None, None, None

        # 分析预测数据
        prediction_data = []
        debug_output = []

        last_signal = None
        last_signal_count = 0
        last_signal_info = None
        last_signal_count_info = 0

        # 处理获取到的预测数据
        for date, prediction in stock_predictions:
            if prediction not in ['涨', '跌']:
                continue

            if last_signal is None:
                last_signal = prediction
                last_signal_count = 1
            elif prediction == last_signal:
                last_signal_count += 1
            else:
                last_signal_info = last_signal
                last_signal_count_info = last_signal_count
                last_signal = prediction
                last_signal_count = 1

            next_date = (datetime.strptime(date, '%Y%m%d') + pd.tseries.offsets.BDay()).strftime('%Y%m%d')
            if next_date in kline_data:
                next_open = kline_data[next_date]['open']

                for offset in range(days_to_check - 1, 0, -1):
                    future_date = (datetime.strptime(next_date, '%Y%m%d') + pd.tseries.offsets.BDay(offset)).strftime(
                        '%Y%m%d')
                    if future_date in kline_data:
                        break
                else:
                    future_date = next_date

                future_close = kline_data[future_date]['close']

                actual_change = '涨' if future_close >= next_open else '跌' if prediction == '涨' else '跌' if future_close <= next_open else '涨'

                prediction_correct = '对' if prediction == actual_change else '错'
                prediction_data.append([
                    stock_code, stock_name, date, prediction, next_date, future_date, actual_change,
                    f"{next_open} / {future_close} / {round((future_close - next_open) / next_open * 100, 2)}%",
                    prediction_correct
                ])
                debug_output.append(
                    f"日期: {date}, 预测: {prediction}, 实际: {next_date} 到 {future_date}, "
                    f"实际涨跌: {actual_change}, 开盘价: {next_open}, "
                    f"收盘价: {future_close}, 涨跌幅: {round((future_close - next_open) / next_open * 100, 2)}%, "
                    f"预测正确: {prediction_correct}"
                )

        # 最后一次信号的处理
        if last_signal is not None:
            last_signal_info = last_signal
            last_signal_count_info = last_signal_count

        # 反转结果以按日期升序显示
        prediction_data.reverse()

        # 计算预测正确率
        correct_predictions = sum(1 for p in prediction_data if p[-1] == '对')
        total_predictions = len(prediction_data)
        accuracy = round(correct_predictions / total_predictions * 100, 2) if total_predictions > 0 else 0.0

        # 返回结果
        return prediction_data, accuracy, debug_output, last_signal_info, last_signal_count_info

    def write_to_text(self, content, prefix, current_date):
        # 获取程序文件所在的绝对路径
        program_dir = CURRENT_DIR
        
        # 构造文件名:前缀+日期
        file_name = f"{prefix}{current_date}.txt"
        # 定义子目录名称并构造完整路径
        subdirectory = "买卖信号"
        subdirectory_path = os.path.join(program_dir, subdirectory)
        # 如果子目录不存在，则创建它
        if not os.path.exists(subdirectory_path):
            os.makedirs(subdirectory_path)
        # 构造文件的完整路径
        file_path = os.path.join(subdirectory_path, file_name)

        # 检查文件是否存在以及内容是否已经存在
        if os.path.exists(file_path):
            try:
                # 首先尝试以 GBK 编码读取
                try:
                    with open(file_path, 'r', encoding='gbk') as file:
                        existing_contents = file.read()
                except UnicodeDecodeError:
                    # 如果 GBK 失败，尝试 UTF-8
                    with open(file_path, 'r', encoding='utf-8') as file:
                        existing_contents = file.read()
                
                if content in existing_contents:
                    return
            except Exception as e:
                print(f"读取文件时出错: {e}")
                return

        # 写入文本文件，使用"a"模式追加内容
        try:
            # 使用 GBK 编码写入
            with open(file_path, "a", encoding='gbk') as file:
                file.write(content + '\n')
        except Exception as e:
            print(f"写入文件时出错: {e}")



    # 文件加载股票列表数据,显示股票列表
    def load_stock_data(self, file_name):
        start_time = time.time()
        
        # 使用完整路径读取 Table.txt
        table_file_path = os.path.join(CURRENT_DIR, f"Table.txt")
        # 1. 批量读取所有股票数据
        stock_data = []
        try:
            with open(table_file_path, 'r', encoding='gbk') as file:  # 使用完整路径
                for line in file:
                    parts = line.strip().split('\t')
                    if len(parts) == 3:
                        stock_data.append({
                            'code': parts[0],
                            'name': parts[1],
                            'industry': parts[2]
                        })
        except Exception as e:
            print(f"读取文件{table_file_path}时出错: {e}")  # 输出完整路径
            return

        # 2. 获取最近的日期目录
        base_path = 'historical_data'
        target_date = datetime.today()
        # 获取最新和次新的目录
        nearest_directory, second_nearest_directory = self.find_nearest_date_directories(base_path, target_date)

        # 3. 批量获取所有股票代码
        stock_codes = [stock['code'] for stock in stock_data]
        stock_names = [stock['name'] for stock in stock_data]

        # 4. 建立数据库连接
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()

        # 5. 批量查询股票价格和变化
        placeholders = ','.join(['%s'] * len(stock_codes))
        cursor.execute(f'''
            SELECT code, name, close_price, change_percentage, market 
            FROM gp_stocks 
            WHERE code IN ({placeholders})
        ''', tuple(stock_codes))
        price_data = {row[0]: row for row in cursor.fetchall()}

        # 6. 批量查询技术指标
        cursor.execute(f'''
            SELECT s.code, i.indicator_value 
            FROM gp_stocks s
            JOIN gp_indicators i ON s.id = i.stock_id
            WHERE s.code IN ({placeholders})
            AND i.date = %s
            AND i.indicator_name = 'FTS'
        ''', (*stock_codes, nearest_directory))
        forecast_data = {row[0]: row[1] for row in cursor.fetchall()}

        # 7. 清除现有数据
        for item in self.stock_tree.get_children():
            self.stock_tree.delete(item)

        # 8. 批量插入数据到 Treeview
        for stock in stock_data:
            code = stock['code']
            name = stock['name']
            if not code or not name:
                print(f"无效的股票数据: {stock}")
                continue

            # 继续处理有效的股票数据
            if code in price_data:
                # 修正数据获取方式
                stock_info = price_data[code]
                code_db, name_db, close_price, change_percentage, market = stock_info
                
                # 格式化价格和百分比，确保显示正确
                try:
                    close_price = float(close_price)
                    change_percentage = float(change_percentage)
                    combined_price_change = f"{close_price:.2f}/{change_percentage:.2f}%"
                except (ValueError, TypeError):
                    combined_price_change = "0.00/0.00%"
                
                forecast = forecast_data.get(code)
               
                # 根据涨跌幅确定颜色
                text_color = 'red' if forecast == '涨' else 'green' if forecast == '跌' else 'black'
                
                self.stock_tree.insert("", tk.END, 
                    values=(code, stock['name'], combined_price_change, stock['industry']), 
                    tags=(text_color,))


        # 9. 配置标签颜色
        for color in ['green', 'red', 'black', 'Magenta']:
            self.stock_tree.tag_configure(color, foreground=color)

        conn.close()
        
        end_time = time.time()
        print(f"函数执行结束时间: {end_time - start_time:.4f} 秒")
        
        self.calculate_percentage_sum()

        # 10.统计颜色 - 修复的部分
        red_count = len([item for item in self.stock_tree.get_children() 
                        if 'red' in self.stock_tree.item(item)['tags']])
        green_count = len([item for item in self.stock_tree.get_children() 
                         if 'green' in self.stock_tree.item(item)['tags']])
        black_count = len([item for item in self.stock_tree.get_children() 
                         if 'black' in self.stock_tree.item(item)['tags']])

        total_count = red_count + green_count + black_count

        # 输出统计结果
        if total_count > 0:
            red_percentage = (red_count / total_count) * 100
            green_percentage = (green_count / total_count) * 100
            black_percentage = (black_count / total_count) * 100





            # 计算趋势强弱状态
            trend_strength = ""
            # 首先判断黑色股票的占比情况
            black_ratio = black_count / total_count
            if black_ratio > 0.4:  # 如果黑色股票占比超过40%
                trend_strength = "观望等待"
            else:
                # 当黑色股票不多时，再判断红绿股票的比例
                if red_count > green_count * 1.5:
                    trend_strength = "强势上涨"
                elif red_count > green_count * 1.2:
                    trend_strength = "温和上涨"
                elif green_count > red_count * 1.5:
                    trend_strength = "强势下跌"
                elif green_count > red_count * 1.2:
                    trend_strength = "温和下跌"
                elif black_ratio > 0.25:  # 黑色股票占比超过25%但小于40%
                    trend_strength = "市场观望"
                else:
                    trend_strength = "震荡整理"


            # 更新窗口标题
            self.root.title(
                f"股票分析 - 红色股票数量: {red_count} ({red_percentage:.2f}%), "
                f"绿色股票数量: {green_count} ({green_percentage:.2f}%), "
                f"黑色股票数量: {black_count} ({black_percentage:.2f}%) - "
                f"市场趋势: {trend_strength}"
            )

        else:
            self.root.title("股票分析 - 没有股票数据可供统计")


    def get_latest_change(self, stock_code, stock_name):
        if not stock_name:  # 如果 stock_name 是空字符串或 None
            stock_name = None

        # 判断 file_path 为空时就返回函数
        file_path = self.find_stock_file(stock_code, stock_name)
        if not file_path:
            print("get_latest_change: 未找到对应的股票数据文件。")
            return None, None, None

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            print(f"连接数据库时出错: {e}")
            return None, None, None

        if "data" in data and "klines" in data["data"]:
            klines = data["data"]["klines"]
            if klines:
                latest_kline = klines[-1].split(',')
                latest_date = latest_kline[0]
                latest_price = latest_kline[2]
                change = latest_kline[8]

                change_value = float(latest_kline[7])
                color = "red" if change_value > 0 else "green"

                return latest_price, change, color

        print(f"get_latest_change：错误 在JSON文件中未找到股票代码 {stock_code} 的有效数据。")
        return None, None, None

    def get_stock_price_change_and_color(self, stock_code, stock_name):

        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT close_price, change_percentage, market
            FROM gp_stocks 
            WHERE code = %s AND name = %s
        ''', (stock_code, stock_name))

        result = cursor.fetchone()
        conn.close()

        if result:
            close_price, change_percentage, market = result
            color = "red" if change_percentage > 0 else "green" if change_percentage < 0 else "black"
            return close_price, change_percentage, market, color
        else:
            print(f"未找到股票代码 {stock_code}  {stock_name} 中的记录。")
            return None, None, None, None


    def show_stock_info(self, event):
        stock_code = None  # 初始化 stock_code 变量
        # 获取所选项的索引
        for selection in self.stock_tree.selection():
            item = self.stock_tree.item(selection)
            values = item['values']
            # 检查值列表是否为空
            if not values:
                print("错误：所选项目无数据。")
                return
            # 检查列表长度是否符合预期
            if len(values) < 4:  # 期望4列数据
                print(f"错误：期望4列数据，但得到 {len(values)} 列。")
                return
            # 提取股票信息
            stock_code = f"{values[0]:0>6}"  # 保留前导零，确保长度为6
            stock_name = str(values[1])
            self.selected_item = item

        # 在使用 stock_code 前检查是否赋值
        if stock_code and len(stock_code) >= 6:
            self.plot_stock_chart(stock_code,stock_name)
        else:
            print("stock_code 的字符长度小于 6 或没有有效的 stock_code")

    # 绘制股票图表数据
    def plot_stock_chart(self, stock_code, stock_name=""):
        import time  # 导入时间模块
        start_time = time.time()  # 记录开始时间

        if not stock_name:  # 如果 stock_name 是空字符串或 None
            stock_name = None

        print(f"plot_stock_chart 参数: stock_code={stock_code}, stock_name={stock_name}")
        file_path = self.find_stock_file(stock_code, stock_name)
        print(f"find_stock_file 返回: {file_path}")

        if file_path:
            self.plot_stock_data(file_path)
        else:
            print("plot_stock_chart: 未找到对应的股票数据文件。")

        end_time = time.time()  # 记录结束时间
        print(f"plot_stock_chart 耗时: {end_time - start_time:.2f} 秒")  # 打印耗时


    # 获取本地股票json文件
    def find_stock_file(self, stock_code, stock_name=""):
        if not stock_name:  # 如果 stock_name 是空字符串或 None
            print("find_stock_file: stock_code 为空")
            stock_name = None

        # 获取当前文件的目录
        current_directory = CURRENT_DIR
        base_path = os.path.join(current_directory, 'historical_data')
        target_date = datetime.today()

        # 获取最新和次新的目录
        nearest_directory, second_nearest_directory = self.find_nearest_date_directories(base_path, target_date)

        if nearest_directory is None:
            print("未找到日期目录")
            return None

        # 获取市场 ID
        market_id = self.get_market_by_stock_code(stock_code, stock_name)

        # 提取股票代码（倒数6位）
        stock_code_id = stock_code[-6:]  # 取字符串的最后6个字符

        # 构建可能的文件路径
        file_path1 = os.path.join(base_path, nearest_directory, f'SZ{stock_code}.json')
        file_path2 = os.path.join(base_path, nearest_directory, f'SH{stock_code}.json')
        file_path3 = os.path.join(base_path, nearest_directory, f'{stock_code}.json')
        file_path4 = os.path.join(base_path, nearest_directory, f'{stock_code_id}.json')

        # 根据 market_id 查找文件
        if market_id == "0":
            file_path1 = os.path.join(base_path, nearest_directory, f'SZ{stock_code_id}.json')
            if os.path.exists(file_path1):
                return file_path1
        elif market_id == "1":
            file_path2 = os.path.join(base_path, nearest_directory, f'SH{stock_code_id}.json')
            if os.path.exists(file_path2):
                return file_path2
        else:
            # 首先尝试在最新目录中查找文件
            if os.path.exists(file_path1):
                return file_path1
            elif os.path.exists(file_path2):
                return file_path2
            elif os.path.exists(file_path3):
                return file_path3
            elif os.path.exists(file_path4):
                return file_path4

        # 如果在最新目录中未找到，尝试在次新目录中查找
        if second_nearest_directory:
            file_path1 = os.path.join(base_path, second_nearest_directory, f'SZ{stock_code_id}.json')
            file_path2 = os.path.join(base_path, second_nearest_directory, f'SH{stock_code_id}.json')
            file_path3 = os.path.join(base_path, second_nearest_directory, f'{stock_code_id}.json')

            if os.path.exists(file_path1):
                return file_path1
            elif os.path.exists(file_path2):
                return file_path2
            elif os.path.exists(file_path3):
                return file_path3

        # 如果仍未找到文件，则输出完整路径
        print(f"未找到文件：{file_path1}")
        print(f"未找到文件：{file_path2}")
        print(f"未找到文件：{file_path3}")   
        print(f"未找到文件：{file_path4}")

        return None

    def get_market_by_stock_code(self, stock_code, stock_name):

        if not stock_code:
            print("get_market_by_stock_code: stock_code 为空")
            return None

        # 排除包含 '=' 符号的股票代码
        if '=' in stock_code:
            return None  # 跳过包含 '=' 的股票代码


        # 4. 建立数据库连接
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()

        # 构建查询语句
        query = "SELECT market FROM gp_stocks WHERE code = %s AND name = %s"

        try:
            # 执行查询
            cursor.execute("SELECT market FROM gp_stocks WHERE code = %s", (stock_code,))
            result = cursor.fetchone()

            # 检查是否有结果
            if result:
                return result[0]  # 返回 market 字段的内容
            else:
                print(f"get_market_by_stock_code: 未找到匹配的记录 [{stock_code}] [{stock_name}]")
                return None
        except sqlite3.Error as e:
            print(f"get_market_by_stock_code: 数据库查询错误: {e}")
            return None
        finally:
            # 关闭连接
            conn.close()

    def find_nearest_date_directories(self, base_path, target_date):
        # 获取当前文件的目录
        current_directory = CURRENT_DIR
        # 构建 historical_data 的完整路径
        full_path = os.path.join(current_directory, base_path)
        # print(f"historical_data 的完整路径: {full_path}")  # 输出完整路径

        # 检查目录是否存在
        if not os.path.exists(full_path):
            print(f"错误: 目录 {full_path} 不存在。请检查路径是否正确。")
            return None, None

        # 获取目录下的所有文件和文件夹
        try:
            directories = os.listdir(full_path)
        except FileNotFoundError as e:
            print(f"错误: {e}")
            return None, None
       
        directories = os.listdir(full_path)
        nearest_directory = None
        second_nearest_directory = None
        min_time_difference = float('inf')
        second_min_time_difference = float('inf')

        for directory in directories:
            try:
                directory_date = datetime.strptime(directory, '%Y%m%d')
                time_difference = abs((target_date - directory_date).days)
                if time_difference < min_time_difference:
                    second_nearest_directory = nearest_directory
                    second_min_time_difference = min_time_difference
                    min_time_difference = time_difference
                    nearest_directory = directory
                elif time_difference < second_min_time_difference:
                    second_min_time_difference = time_difference
                    second_nearest_directory = directory
            except ValueError:
                continue
        return nearest_directory, second_nearest_directory

    # 界面上 绘制股票数据
    def plot_stock_data(self, file_path):
        import time  # 导入时间模块
        start_time = time.time()  # 记录开始时间


        with open(file_path, 'r', encoding='utf-8') as file:
            json_data = json.load(file)

        # 获取股票代码和名称
        stock_code = json_data['data']['code']
        stock_name = json_data['data']['name']
        stock_market = json_data['data']['market']
        stock_decimal = json_data['data']['decimal']
        stock_dktotal = json_data['data']['dktotal']
        stock_preKPrice = json_data['data']['preKPrice']
        klines_all = json_data['data']['klines']

        # 获取最后一行的数据
        last_row = klines_all[-1]
        # 提取最后一行的日期字段
        last_date = last_row.split(',')[0]
        # 转换日期格式为 YYYYMMDD
        Stock_date_time = last_date.replace('-', '')

        period = int(self.period_var.get())
        #print(period)
        klines = klines_all[-period:]
        #print(klines)



        dates = [entry.split(',')[0] for entry in klines]
        # 股票的开盘价
        open_prices = [float(entry.split(',')[1]) for entry in klines]
        # 股票的收盘价
        closing_prices = [float(entry.split(',')[2]) for entry in klines]
        volumes = [float(entry.split(',')[5]) for entry in klines]
        # 换手率数据（倒数第2项）
        turnover_rates = [float(entry.split(',')[-2]) for entry in klines]
        # Change_Percentage = [float(entry.split(',')[8]) for entry in klines]

        # 计算RSI
        # rsi = self.calculate_rsi(closing_prices)

        # 清除画布并设置字体
        self.figure.clear()
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 使用微软雅黑字体

        # 创建日期对象和标签
        # 将日期字符串转换为日期对象
        date_objs = [datetime.strptime(date, '%Y-%m-%d') for date in dates]
        # 提取日期中的日
        day_labels_r = [date_obj.day for date_obj in date_objs]
        # 提取日期中的 月-日
        day_labels = [datetime.strptime(date, '%Y-%m-%d').strftime('%m-%d') for date in dates]

        # 收盘价格图
        ax1 = self.figure.add_subplot(511)
        ax1.plot(dates, closing_prices, label='收盘价', color='blue', linewidth=1.5)

        # 添加趋势策略垂直线
        # 获取趋势预测数据
        Stock_date_time_ids = [date.replace('-', '') for date in dates]
        results = self.query_indicators_batch(stock_code, stock_market, Stock_date_time_ids, "FTS")

        # 将结果存储到字典中
        forecast_data = {}
        for row in results:
            date = row[0]
            indicator_value = row[1]
            forecast_data[date] = indicator_value

        # 获取y轴范围用于绘制垂直线
        y_min, y_max = ax1.get_ylim() if hasattr(ax1, 'get_ylim') else (min(closing_prices), max(closing_prices))

        # 为每个交易日绘制趋势策略垂直线
        for i, (date, price) in enumerate(zip(dates, closing_prices)):
            Stock_date_time_id = date.replace('-', '')
            forecast = forecast_data.get(Stock_date_time_id, None)

            # 根据趋势策略绘制垂直线
            if forecast == '涨':
                # 红色垂直线：从股价点到图表底边
                ax1.plot([i, i], [price, y_min], color='red', linewidth=1.5, alpha=0.7)
            elif forecast == '跌':
                # 绿色垂直线：从股价点到图表底边
                ax1.plot([i, i], [price, y_min], color='green', linewidth=1.5, alpha=0.7)
            # 黑色（无明确趋势）不绘制垂直线

        # ax1.set_xlabel('日期')
        ax1.set_ylabel('收盘价')
        ax1.set_title(f'{stock_name}（股票代码：{stock_code}）收盘价格走势图 + 趋势策略线')
        # 设置x轴刻度和标签
        ax1.set_xticks(range(len(dates)))
        ax1.set_xticklabels(day_labels, rotation=90, ha='right', rotation_mode='anchor')
        # 使用 MaxNLocator 来设置 y 轴的刻度为 5 个刻度
        ax1.yaxis.set_major_locator(ticker.MaxNLocator(nbins=5))
        ax1.legend()
        ax1.grid(True)

        # 成交量图 + SAR指标 + 换手率
        ax2 = self.figure.add_subplot(512)

        # 绘制成交量柱状图
        colors = ['red' if close > open else 'green' for open, close in zip(open_prices, closing_prices)]
        ax2.bar(dates, volumes, label='成交量', color=colors, alpha=0.5)

        # 创建第二个y轴用于SAR指标
        ax2_twin = ax2.twinx()

        # 绘制换手率趋势线
        try:
            # 在右侧y轴绘制换手率曲线，使用天蓝色 #00b9f1
            ax2_twin.plot(range(len(dates)), turnover_rates, color='#3466db', linewidth=2,
                         label='换手率(%)', alpha=0.8, marker='o', markersize=3)

            # 设置换手率y轴范围：让换手率占成交量图的80%高度
            max_turnover = max(turnover_rates) if turnover_rates else 5.0
            # 计算y轴最大值，使换手率占80%高度
            ax2_twin.set_ylim(0, max_turnover * 1.25)

        except Exception as e:
            print(f"换手率绘制失败: {e}")

        # 计算并绘制SAR指标
        try:
            # 获取最高价和最低价数据
            high_prices = [float(entry.split(',')[3]) for entry in klines]
            low_prices = [float(entry.split(',')[4]) for entry in klines]

            # 使用talib计算SAR指标
            import talib
            sar_values = talib.SAR(np.array(high_prices), np.array(low_prices), acceleration=0.02, maximum=0.2)

            # 获取成交量图的y轴范围，用于SAR指标的显示范围
            volume_min = min(volumes)
            volume_max = max(volumes)
            volume_range = volume_max - volume_min

            # 获取SAR值的范围
            valid_sar = [sar for sar in sar_values if not np.isnan(sar)]
            if valid_sar:
                sar_min = min(valid_sar)
                sar_max = max(valid_sar)
                sar_range = sar_max - sar_min if sar_max != sar_min else 1

                # 绘制SAR指标点，映射到成交量图的整个高度范围
                for i, (close_price, sar_value) in enumerate(zip(closing_prices, sar_values)):
                    if not np.isnan(sar_value):
                        # 将SAR值映射到成交量图的高度范围
                        # 使用80%的图表高度，留出20%的空间给换手率
                        normalized_sar = (sar_value - sar_min) / sar_range
                        mapped_sar_height = volume_min + normalized_sar * volume_range * 0.8

                        if close_price > sar_value:
                            # 收盘价高于SAR，显示红色点（看涨信号）
                            ax2.scatter(i, mapped_sar_height, color='red', s=40, alpha=0.9, marker='o',
                                      edgecolors='darkred', linewidth=1, zorder=5)
                        else:
                            # 收盘价低于SAR，显示绿色点（看跌信号）
                            ax2.scatter(i, mapped_sar_height, color='green', s=40, alpha=0.9, marker='o',
                                      edgecolors='darkgreen', linewidth=1, zorder=5)



        except Exception as e:
            print(f"SAR/DPO指标计算失败: {e}")

        # 设置成交量/SAR轴（左侧）
        ax2.set_ylabel('成交量 / SAR指标', color='gray')
        ax2.tick_params(axis='y', labelcolor='gray')
        ax2.set_xticks(range(len(dates)))
        ax2.set_xticklabels(day_labels_r, rotation=0, ha='right', rotation_mode='anchor')
        ax2.yaxis.set_major_locator(ticker.MaxNLocator(nbins=5))

        # 设置换手率轴（右侧）
        ax2_twin.set_ylabel('换手率(%)', color='#00b9f1')
        ax2_twin.tick_params(axis='y', labelcolor='#00b9f1')
        ax2_twin.yaxis.set_major_locator(ticker.MaxNLocator(nbins=5))

        # 设置标题和图例
        ax2.set_title('成交量 + SAR指标 + 换手率')

        # 合并图例
        lines1, labels1 = ax2.get_legend_handles_labels()
        lines2, labels2 = ax2_twin.get_legend_handles_labels()

        # 添加SAR指标到图例
        from matplotlib.lines import Line2D
        sar_up = Line2D([0], [0], marker='o', color='w', markerfacecolor='red', markersize=8, label='SAR看涨')
        sar_down = Line2D([0], [0], marker='o', color='w', markerfacecolor='green', markersize=8, label='SAR看跌')

        all_lines = lines1 + lines2 + [sar_up, sar_down]
        all_labels = labels1 + labels2 + ['SAR看涨', 'SAR看跌']

        ax2.legend(all_lines, all_labels, loc='upper left')
        ax2.grid(True, alpha=0.3)




        # MACD 图表一，代码开始++++++++++++++++++++++++++++++++++++++++++
        # 专门用于MACD指标的数据长度增加40个
        total_days = period + 60
        klines_macd = klines_all[-total_days:]
        # 解析数据
        dates_macd = []
        closes_macd = []

        for kline in klines_macd:
            parts = kline.split(',')
            dates_macd.append(parts[0])  # 日期
            closes_macd.append(float(parts[2]))  # 收盘价信息

        # 创建DataFrame
        df_macd = pd.DataFrame({
            'Date': dates_macd,
            'Close': closes_macd,
        })

        # 截取最新的 total_days 天数据用于计算MACD指标
        df_macd_1 = df_macd.tail(total_days).copy()

        # MACD指标的参数设置
        macd_1 = (6, 13, 5)     # MACD 短期参数
        macd_2 = (12, 23, 10)   # MACD 中期参数
        macd_3 = (15, 28, 13)   # MACD 中长期参数

        macd_1_short, macd_1_long, macd_1_signal = macd_1
        macd_2_short, macd_2_long, macd_2_signal = macd_2
        macd_3_short, macd_3_long, macd_3_signal = macd_3

        macd_line, signal_line, macd_histogram = self.calculate_macd(df_macd_1['Close'], macd_1_short, macd_1_long, macd_1_signal)
        df_macd_1['MACD'] = macd_line
        df_macd_1['Signal Line'] = signal_line
        df_macd_1['MACD Histogram'] = macd_histogram

        ax3 = self.figure.add_subplot(513)
        # 绘制MACD指标线和柱状图
        ax3.plot(df_macd_1['Date'].tail(period), df_macd_1['MACD'].tail(period), label='快线', color='red')
        ax3.plot(df_macd_1['Date'].tail(period), df_macd_1['Signal Line'].tail(period), label='慢线', color='green')

        # 绘制MACD柱状图，零轴上方为红色，下方为绿色
        for i in range(len(df_macd_1['Date'].tail(period))):
            if df_macd_1['MACD Histogram'].iloc[-period + i] >= 0:
                ax3.bar(df_macd_1['Date'].iloc[-period + i], df_macd_1['MACD Histogram'].iloc[-period + i], color='red',
                        alpha=0.7)
            else:
                ax3.bar(df_macd_1['Date'].iloc[-period + i], df_macd_1['MACD Histogram'].iloc[-period + i],
                        color='green', alpha=0.7)

        #ax3.set_xlabel('日期')
        # ax3.set_title(f"MACD参数: {macd_1_short}.{macd_1_long}.{macd_1_signal}")
        # 设置Y轴标签和颜色
        ax3.set_ylabel(f'MACD:{macd_1_short}.{macd_1_long}.{macd_1_signal}', color='black')
        ax3.tick_params(axis='y', labelcolor='black')
        # 设置x轴刻度和标签
        ax3.set_xticks(range(len(dates)))
        # 设置日期显示标签
        ax3.set_xticklabels(day_labels_r, rotation=0, ha='right', rotation_mode='anchor')
        # 添加零轴
        ax3.axhline(y=0, color='gray', linestyle='--')
        # 使用 MaxNLocator 来设置 y 轴的刻度为 5 个刻度
        ax3.yaxis.set_major_locator(ticker.MaxNLocator(nbins=5))
        ax3.legend()
        ax3.grid(True)


        Macd_trading_index_number=0     #macd买卖指标最终数值
        Macd_trading_number=0       #macd买卖指标临时数值

        # # 标注MACD的买卖点
        # 遍历 MACD 柱状图数据，从第二个数据点开始
        for i in range(61, len(macd_histogram)):
            # 如果当前柱状图数据大于0且前一个柱状图数据小于0
            if macd_histogram[i] > 0 and macd_histogram[i - 1] < 0:
                # 在图表上标注买入信号，显示收盘价格，并附上箭头
                ax3.annotate(
                    f'{df_macd_1["Close"][i]}\n买',  # 标注文本，显示收盘价格并注明买入
                    xy=(df_macd_1['Date'][i], macd_histogram[i]),  # 标注位置，位于当前日期和MACD柱状图位置
                    xytext=(df_macd_1['Date'][i], macd_histogram[i]),  # 标注文本的位置，略高于标注位置
                    ha='center',  # 水平居中对齐
                    arrowprops=dict(facecolor='red', shrink=0.02)  # 箭头属性，设置箭头颜色为红色
                )
                Macd_trading_number = 30  # 设置交易数量为30

            elif macd_histogram[i] < 0 and macd_histogram[i - 1] > 0:
                ax3.annotate(f'{df_macd_1["Close"][i]}\n卖', xy=(df_macd_1['Date'][i], macd_histogram[i]),
                             xytext=(df_macd_1['Date'][i], macd_histogram[i]),
                             ha='center',  # 水平居中对齐
                             arrowprops=dict(facecolor='green'))
                Macd_trading_number = -30

        Macd_trading_index_number += Macd_trading_number     #macd 买卖指标数值
        # MACD 图表一，代码结束++++++++++++++++++++++++++++++++++++++++++



        macd_line, signal_line, macd_histogram = self.calculate_macd(df_macd_1['Close'], macd_2_short, macd_2_long, macd_2_signal)
        df_macd_1['MACD'] = macd_line
        df_macd_1['Signal Line'] = signal_line
        df_macd_1['MACD Histogram'] = macd_histogram
        # 增加的第5图形,第二种MACD曲线图  # 特别注意：此处的参数是设置MACD指标参数12，23，10
        ax5 = self.figure.add_subplot(514)
        # 绘制MACD指标线和柱状图
        ax5.plot(df_macd_1['Date'].tail(period), df_macd_1['MACD'].tail(period), label='快线', color='red')
        ax5.plot(df_macd_1['Date'].tail(period), df_macd_1['Signal Line'].tail(period), label='慢线', color='green')
        # 绘制MACD柱状图，零轴上方为红色，下方为绿色
        for i in range(len(df_macd_1['Date'].tail(period))):
            if df_macd_1['MACD Histogram'].iloc[-period + i] >= 0:
                ax5.bar(df_macd_1['Date'].iloc[-period + i], df_macd_1['MACD Histogram'].iloc[-period + i], color='red',
                        alpha=0.7)
            else:
                ax5.bar(df_macd_1['Date'].iloc[-period + i], df_macd_1['MACD Histogram'].iloc[-period + i],
                        color='green', alpha=0.7)

        # ax5.set_title(f"MACD参数: {macd_2_short}.{macd_2_long}.{macd_2_signal}")
        # 设置Y轴标签和颜色
        ax5.set_ylabel(f'MACD:{macd_2_short}.{macd_2_long}.{macd_2_signal}', color='black')
        ax5.tick_params(axis='y', labelcolor='black')
        # 设置x轴刻度和标签
        ax5.set_xticks(range(len(dates)))
        # 设置日期显示标签
        ax5.set_xticklabels(day_labels_r, rotation=0, ha='right', rotation_mode='anchor')
        ax5.axhline(y=0, color='gray', linestyle='--')
        # 使用 MaxNLocator 来设置 y 轴的刻度为 5 个刻度
        ax5.yaxis.set_major_locator(ticker.MaxNLocator(nbins=5))

        ax5.legend()
        ax5.grid(True)

        for i in range(61, len(macd_histogram)):
            # 如果当前柱状图数据大于0且前一个柱状图数据小于0
            if macd_histogram[i] > 0 and macd_histogram[i - 1] < 0:
                # 在图表上标注买入信号，显示收盘价格，并附上箭头
                ax5.annotate(
                    f'{df_macd_1["Close"][i]}\n买',  # 标注文本，显示收盘价格并注明买入
                    xy=(df_macd_1['Date'][i], macd_histogram[i]),  # 标注位置，位于当前日期和MACD柱状图位置
                    xytext=(df_macd_1['Date'][i], macd_histogram[i]),  # 标注文本的位置，略高于标注位置
                    ha='center',  # 水平居中对齐
                    arrowprops=dict(facecolor='red')  # 箭头属性，设置箭头颜色为红色，收缩比例为0.02
                )
                Macd_trading_number = 30  # 设置交易数量为30

            elif macd_histogram[i] < 0 and macd_histogram[i - 1] > 0:
                ax5.annotate(f'{df_macd_1["Close"][i]}\n卖', xy=(df_macd_1['Date'][i], macd_histogram[i]),
                             xytext=(df_macd_1['Date'][i], macd_histogram[i]),
                             ha='center',  # 水平居中对齐
                             arrowprops=dict(facecolor='green'))
                Macd_trading_number = -30

        Macd_trading_index_number += Macd_trading_number     #macd 买卖指标数值


        # -----------------------------------------------------
        macd_line, signal_line, macd_histogram = self.calculate_macd(df_macd_1['Close'], macd_3_short, macd_3_long, macd_3_signal)
        df_macd_1['MACD'] = macd_line
        df_macd_1['Signal Line'] = signal_line
        df_macd_1['MACD Histogram'] = macd_histogram
        # 增加的第6图形,第三种MACD曲线图  # 特别注意：此处的参数是设置MACD指标参数15，28，13
        ax6 = self.figure.add_subplot(515)
        # 绘制MACD指标线和柱状图
        ax6.plot(df_macd_1['Date'].tail(period), df_macd_1['MACD'].tail(period), label='快线', color='red')
        ax6.plot(df_macd_1['Date'].tail(period), df_macd_1['Signal Line'].tail(period), label='慢线', color='green')
        # 绘制MACD柱状图，零轴上方为红色，下方为绿色
        for i in range(len(df_macd_1['Date'].tail(period))):
            if df_macd_1['MACD Histogram'].iloc[-period + i] >= 0:
                ax6.bar(df_macd_1['Date'].iloc[-period + i], df_macd_1['MACD Histogram'].iloc[-period + i], color='red',
                        alpha=0.7)
            else:
                ax6.bar(df_macd_1['Date'].iloc[-period + i], df_macd_1['MACD Histogram'].iloc[-period + i],
                        color='green', alpha=0.7)

        # ax6.set_title(f"MACD参数: {macd_3_short}.{macd_3_long}.{macd_3_signal}")
        # 设置Y轴标签和颜色
        ax6.set_ylabel(f'MACD:{macd_3_short}.{macd_3_long}.{macd_3_signal}', color='black')
        ax6.tick_params(axis='y', labelcolor='black')
        # 设置x轴刻度和标签
        ax6.set_xticks(range(len(dates)))
        # 设置日期显示标签
        ax6.set_xticklabels(day_labels_r, rotation=0, ha='right', rotation_mode='anchor')
        ax6.axhline(y=0, color='gray', linestyle='--')
        # 使用 MaxNLocator 来设置 y 轴的刻度为 5 个刻度
        ax6.yaxis.set_major_locator(ticker.MaxNLocator(nbins=5))

        ax6.legend()
        ax6.grid(True)


        for i in range(61, len(macd_histogram)):
            # 如果当前柱状图数据大于0且前一个柱状图数据小于0
            if macd_histogram[i] > 0 and macd_histogram[i - 1] < 0:
                # 在图表上标注买入信号，显示收盘价格，并附上箭头
                ax6.annotate(
                    f'{df_macd_1["Close"][i]}\n买',  # 标注文本，显示收盘价格并注明买入
                    xy=(df_macd_1['Date'][i], macd_histogram[i]),  # 标注位置，位于当前日期和MACD柱状图位置
                    xytext=(df_macd_1['Date'][i], macd_histogram[i]),  # 标注文本的位置，略高于标注位置
                    ha='center',
                    arrowprops=dict(facecolor='red')  # 箭头属性，设置箭头颜色为红色，收缩比例为0.02
                )
                Macd_trading_number = 30  # 设置交易数量为30

            elif macd_histogram[i] < 0 and macd_histogram[i - 1] > 0:
                ax6.annotate(f'{df_macd_1["Close"][i]}\n卖', xy=(df_macd_1['Date'][i], macd_histogram[i]),
                             xytext=(df_macd_1['Date'][i], macd_histogram[i]),
                             ha='center',
                             arrowprops=dict(facecolor='green'))
                Macd_trading_number = -30

        Macd_trading_index_number += Macd_trading_number  # macd 买卖指标数值



        # # =============================================================================================
        # # 标记图形六 每日K线走势图,买卖点,收盘价格    （消耗 0.42秒）
        Stock_date_time_ids = [date.replace('-', '') for date in dates]
        # 使用新的批量查询函数
        forecast_data = {}
        results = self.query_indicators_batch(stock_code, stock_market, Stock_date_time_ids, "FTS")



        # 将结果存储到字典中
        for row in results:
            date = row[0]
            indicator_value = row[1]
            forecast_data[date] = indicator_value

        # 假设 dates 和 closing_prices 是列表，转换为 NumPy 数组
        dates_array = np.array(dates)
        closing_prices_array = np.array(closing_prices)

        # 准备绘图数据
        annotations = []
        for i in range(len(dates_array)):
            Stock_date_time_id = dates_array[i].replace('-', '')
            
            # 直接从字典中获取预测数据
            forecast = forecast_data.get(Stock_date_time_id, None)

            # 判断买卖点标注位置,设置字体颜色
            if forecast == '涨':
                text_color = 'Magenta'  # 标注文本为洋红Magenta
            elif forecast == '跌':
                text_color = 'green'  # 标注文本为绿色
            else:
                text_color = 'black'  # 标注文本为黑色

            # 收集注释信息
            annotations.append((i, closing_prices_array[i], text_color))

        # 批量绘制注释
        for i, price, text_color in annotations:
            ax1.annotate(
                f'{price}',  # 要标注的文本内容，这里格式化显示价格，保留两位小数
                xy=(i, price),  # 标注的点 (x, y) 坐标，即在第 i 天的 price 位置
                xytext=(i, price),  # 标注文本的位置，和标注点的位置一致
                horizontalalignment='right',  # 标注文本的水平对齐方式，右对齐
                verticalalignment='bottom',  # 标注文本的垂直对齐方式，底部对齐
                color=text_color,  # 设置标注文本的颜色
                fontsize=8  # 设置标注文本的字体大小
            )
        # =============================================================================================

        end_time = time.time()  # 记录结束时间
        print(f"plot_stock_data 耗时2: {end_time - start_time:.2f} 秒")  # 打印耗时

        # 2024-07-05 增加统计股票预测准确率  （耗时0.46秒）
        json_file_path = file_path
        
        prediction_data, accuracy, debug_output, last_signal_info, last_signal_count_info = self.analyze_stock_predictions(
            json_file_path)
        # print(f"预测正确率：{accuracy}")


        # 输出最近3个交易日是否有买入和卖出信号的日志
        # 获取数据库中 DPO指标 最后的决策内容（耗时0.5秒）
        # dpo_last_decision = self.query_indicator(stock_code, stock_market, Stock_date_time, "DPO")
        # if dpo_last_decision:
        #     print(f"{stock_name}[{stock_code}]: 【DPO技术指标】最后决策是: {dpo_last_decision}"),

        # 获取数据库中 SAR指标 最后的决策内容（耗时0.5秒）
        # sar_last_decision = self.query_indicator(stock_code, stock_market, Stock_date_time, "SAR")
        # if sar_last_decision:
        #     print(f"{stock_name}[{stock_code}]: 【SAR技术指标】最后决策是: {sar_last_decision}")


        # 批量获取数据库中 DPO，SAR指标 最后的决策内容（耗时0.5秒）
        decisions = self.query_indicator_piliang(stock_code, stock_market, Stock_date_time, ["DPO", "SAR"])
        dpo_last_decision = decisions.get("DPO")
        sar_last_decision = decisions.get("SAR")


        end_time = time.time()  # 记录结束时间
        print(f"plot_stock_data 耗时3: {end_time - start_time:.2f} 秒")  # 打印耗时

        # stock_close_price, stock_change_percentage, stock_color = self.get_latest_change(stock_code, stock_name)
        # # 更新数据库中股票的收盘价和涨跌幅
        # self.update_stock_price_and_change(stock_code, stock_market, stock_close_price, stock_change_percentage)
        # -----------------------------------------------------
        if Macd_trading_index_number > 80 and sar_last_decision == '红' and dpo_last_decision == '红':
            ax6.set_title(f'[买入] MACD趋势:{Macd_trading_index_number}  SAR指标:{sar_last_decision}  DP0指标:{dpo_last_decision}', color='red')
            print(f'{stock_name}[{stock_code}]  【买入】 macd 指标数值:{Macd_trading_index_number}%')
        elif Macd_trading_index_number < 50 and sar_last_decision == '绿' and dpo_last_decision == '绿':
            ax6.set_title(f'[卖出] MACD趋势:{Macd_trading_index_number}  SAR指标:{sar_last_decision}  DP0指标:{dpo_last_decision}', color='green')
            print(f'{stock_name}[{stock_code}]  【卖出】 macd 指标数值:{Macd_trading_index_number}%')

        ax1.set_title(f'{stock_name}[{stock_code}]: 趋势策略准确率 {accuracy}%  当前趋势:{last_signal_info}[{last_signal_count_info}]  SAR指标:{sar_last_decision}  DP0指标:{dpo_last_decision}')
        
 
        self.figure.tight_layout()
        self.figure_canvas.draw()

                # 计算耗时
        end_time = time.time()  # 记录结束时间
        print(f"plot_stock_data 耗时: {end_time - start_time:.2f} 秒")  # 打印耗时
        print("-" * 60)



    # 特别注意：此处的参数是设置MACD指标参数10，23，8
    def calculate_macd(self, closing_prices, short_window=10, long_window=23, signal_window=8):
        # 计算短周期的指数移动平均（EMA），周期为short_window
        short_ema = pd.Series(closing_prices).ewm(span=short_window, adjust=True).mean()
        # 计算长周期的指数移动平均（EMA），周期为long_window
        long_ema = pd.Series(closing_prices).ewm(span=long_window, adjust=True).mean()
        # 计算MACD线，即短周期EMA减去长周期EMA
        macd_line = short_ema - long_ema
        # 计算信号线，即MACD线的EMA，周期为signal_window
        signal_line = macd_line.ewm(span=signal_window, adjust=True).mean()
        # 计算MACD柱，即MACD线减去信号线
        macd_histogram = macd_line - signal_line
        # 返回MACD线，信号线和MACD柱
        return macd_line, signal_line, macd_histogram



    def calculate_rsi(self, closing_prices, window=14):
        deltas = np.diff(closing_prices)
        gains = deltas[deltas >= 0]
        losses = -deltas[deltas < 0]

        avg_gain = np.mean(gains[:window])
        avg_loss = np.mean(losses[:window])

        rs = avg_gain / avg_loss
        rsi = np.zeros_like(closing_prices)
        rsi[:window] = 100.0 - (100.0 / (1.0 + rs))

        for i in range(window, len(closing_prices)):
            delta = deltas[i - 1]

            if delta > 0:
                avg_gain = (avg_gain * (window - 1) + delta) / window
                avg_loss = (avg_loss * (window - 1)) / window
            else:
                avg_gain = (avg_gain * (window - 1)) / window
                avg_loss = (avg_loss * (window - 1) - delta) / window

            rs = avg_gain / avg_loss
            rsi[i] = 100.0 - (100.0 / (1.0 + rs))

        return rsi



    # 下面是抓取每日股票数据的代码部分
    def getData(self, baseUrl, headers):
        response = requests.get(url=baseUrl, headers=headers)
        data = json.loads(response.text)['data']['diff']
        result = []
        for key, value in data.items():
            value['f2'] = '%.2f' % (value['f2'] / 100)
            value['f3'] = '%.2f' % (value['f3'] / 100) + '%'
            value['f4'] = '%.2f' % (value['f4'] / 100)
            value['f5'] = '%.2f' % (value['f5'] / 10000)
            # value['f5'] = '%.2f' % (value['f5']/10000) + '万'
            value['f6'] = '%.2f' % (value['f6'] / 100000000)
            # value['f6'] = '%.2f' % (value['f6']/100000000) + '亿'
            value['f7'] = '%.2f' % (value['f7'] / 100) + '%'
            value['f15'] = '%.2f' % (value['f15'] / 100)
            value['f16'] = '%.2f' % (value['f16'] / 100)
            value['f17'] = '%.2f' % (value['f17'] / 100)
            value['f18'] = '%.2f' % (value['f18'] / 100)
            value['f10'] = '%.2f' % (value['f10'] / 100)
            value['f8'] = '%.2f' % (value['f8'] / 100) + '%'
            value['f9'] = '%.2f' % (value['f9'] / 100)
            value['f23'] = '%.2f' % (value['f23'] / 100)
            result.append(
                [key, value['f12'], value['f14'], value['f2'], value['f3'], value['f4'], value['f5'], value['f6'],
                 value['f7'], value['f15'], value['f16'], value['f17'], value['f18'], value['f10'], value['f8'],
                 value['f9'], value['f23']])
        return result

    def printData(self, result):
        table = PrettyTable()
        table.field_names = ["序号", "代码", "名称", "最新价", "涨跌幅", "涨跌额", "成交量(万手)", "成交额(亿)", "振幅",
                             "最高", "最低", "今开", "昨收", "量比", "换手率", "市盈率（动态）", "市净率"]
        table.add_rows(result)
        print(table)

    def saveData(self, result):
        # 获取当前日期并将其格式化为指定的日期字符串
        today_date = datetime.today().strftime('%Y%m%d')
        dir_path = './csv'
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        # 构造带有日期的文件名
        file_name = f'{dir_path}/A股股票数据_{today_date}.csv'
        # 使用UTF-8编码打开文件，确保支持中文等特殊字符
        with open(file_name, 'w', encoding='utf-8', newline='') as file:
            # 创建 CSV writer，设置 quoting 参数为 QUOTE_NONNUMERIC
            writer = csv.writer(file, quoting=csv.QUOTE_NONNUMERIC)
            # 写入表头
            writer.writerow(
                ["序号", "代码", "名称", "最新价", "涨跌幅", "涨跌额", "成交量(万手)", "成交额(亿)", "振幅", "最高",
                 "最低", "今开", "昨收", "量比", "换手率", "市盈率（动态）", "市净率"])
            # 写入数据
            writer.writerows(result)

    def saveAsSQLite(self, result):
        # 获取当前日期并将其格式化为指定的日期字符串
        today_date = datetime.today().strftime('%Y%m%d')
        dir_path = './db'
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        # 构造带有日期的文件名
        db_name = f'{dir_path}/A股股票数据_{today_date}.db'
        if os.path.exists(db_name):
            # 删除文件
            os.remove(db_name)
        # 连接到 SQLite 数据库
        conn = sqlite3.connect(db_name)
        # 创建游标对象
        cursor = conn.cursor()
        # 创建表格（如果不存在）
        table_name = f'gp_stocks'
        # table_name = f'gp_stocks_{today_date}'
        cursor.execute(f'''CREATE TABLE IF NOT EXISTS {table_name} (
                            id INTEGER PRIMARY KEY,
                            code TEXT,
                            name TEXT,
                            price REAL,
                            change_rate TEXT,
                            change_amount REAL,
                            volume TEXT,
                            amount TEXT,
                            amplitude TEXT,
                            high REAL,
                            low REAL,
                            open REAL,
                            close REAL,
                            volume_ratio REAL,
                            turnover_rate TEXT,
                            pe_ratio REAL,
                            pb_ratio REAL
                        )''')
        # 插入数据
        cursor.executemany(f'''INSERT INTO {table_name} (code, name, price, change_rate, change_amount, volume, amount, amplitude, high, low, open, close, volume_ratio, turnover_rate, pe_ratio, pb_ratio)
                              VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)''', [row[1:] for row in result])
        # 提交事务
        conn.commit()
        # 关闭连接
        conn.close()

    def saveAsJSON(self, result):
        # 获取当前日期并将其格式化为指定的日期字符串
        today_date = datetime.today().strftime('%Y%m%d')
        dir_path = './json'
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
        # 构造带有日期的文件名
        file_name = f'{dir_path}/A股股票数据_{today_date}.json'
        # 写入 JSON 文件
        with open(file_name, 'w', encoding='utf-8') as file:
            json.dump(result, file, ensure_ascii=False, indent=4)

    def a_all_date(self):
        baseUrl = 'https://22.push2.eastmoney.com/api/qt/clist/get%spn=1&pz=6000&po=1&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81+s:2048'
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'}
        result = self.getData(baseUrl, headers)
        self.printData(result)
        self.saveData(result)
        self.saveAsJSON(result)
        self.saveAsSQLite(result)

    def http_get_stock_data_bak(self, secid):
        url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"
        params = {
            "cb": "jQuery351043472769495360547_1716288724686",
            "secid": secid,
            "ut": "fa5fd1943c7b386f172d6893dbfba10b",
            "fields1": "f1,f2,f3,f4,f5,f6",
            "fields2": "f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61",
            "klt": "101",
            "fqt": "1",
            "end": "20500101",
            "lmt": "256",
            "_": "1716288724710"
        }

        response = requests.get(url, params=params)

        if response.status_code == 200:
            json_data = response.text
            start_idx = json_data.index('(') + 1
            end_idx = json_data.rindex(')')
            json_data = json_data[start_idx:end_idx]
            stock_data = json.loads(json_data)

            if len(response.content) < 256:
                return None
            else:
                return stock_data
        else:
            return None


    def http_get_stock_data(self, secid):
        url = "https://push2his.eastmoney.com/api/qt/stock/kline/get"
        params = {
            "cb": "jQuery351043472769495360547_1716288724686",
            "secid": secid,
            "ut": "fa5fd1943c7b386f172d6893dbfba10b",
            "fields1": "f1,f2,f3,f4,f5,f6",
            "fields2": "f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61",
            "klt": "101",
            "fqt": "1",
            "end": "20500101",
            "lmt": "256",
            "_": "1716288724710"
        }
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
        }
        for i in range(2):
            try:
                response = requests.get(url, params=params, headers=headers, timeout=10)
                if response.status_code == 200:
                    json_data = response.text
                    start_idx = json_data.index('(') + 1
                    end_idx = json_data.rindex(')')
                    json_data = json_data[start_idx:end_idx]
                    stock_data = json.loads(json_data)
                    if len(response.content) < 256:
                        return None
                    else:
                        return stock_data
                else:
                    print(f"请求失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"第{i+1}次尝试失败: {e}")
                time.sleep(1)
        print("多次尝试后仍然失败")
        return None



    # 保存数据到文件
    def save_data_to_file(self, data, fine_name, directory):
        if not os.path.exists(directory):
            os.makedirs(directory)

        file_path = os.path.join(directory, f"{fine_name}.json")
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=4, ensure_ascii=False)
        # print(f"数据已保存到 {file_path}")
        return file_path


    def extract_last_date(self, data):

        if not data or 'data' not in data or 'klines' not in data['data'] or not data['data']['klines']:
            print("数据为空或格式不正确，跳过分析部分")
            # 获取程序目录下的 historical_data 目录
            historical_data_dir = os.path.join(CURRENT_DIR, "historical_data")
            if os.path.exists(historical_data_dir):
                # 获取最新的目录名
                directories = [d for d in os.listdir(historical_data_dir) if os.path.isdir(os.path.join(historical_data_dir, d))]
                if directories:
                    latest_directory = sorted(directories, reverse=True)[0]
                    print(f"使用最新的目录名作为日期: {latest_directory}")
                    return latest_directory
                else:
                    print("historical_data 目录为空，无法获取最新日期")
            else:
                print("historical_data 目录不存在，无法获取最新日期")
            return None

        # 确保输入是字典对象，如果是字符串则解析
        if isinstance(data, str):
            import json
            data = json.loads(data)
        # 提取 klines 列表
        klines = data['data']['klines']
        # 获取最后一行数据
        last_line = klines[-1]
        # 提取日期部分
        last_date = last_line.split(',')[0].replace('-', '')

        return last_date



    def on_download_data_click(self):
        threading.Thread(target=self.download_latest_zip, daemon=True).start()

    def download_latest_zip(self):
        # 禁用按钮，防止重复点击
        self.download_data_button.config(state='disabled', text='正在获取数据包信息...')

        # 共享路径
        shared_path = r"\\*************\docker\BaoTa\wwwroot\python\Stock-analysis\data_zip"

        try:
            print("正在获取最新数据包信息...")
            # 验证共享路径是否存在
            if not os.path.exists(shared_path):
                raise FileNotFoundError(f"共享路径无效或无法访问: {shared_path}")

            # 获取共享路径下的所有ZIP文件
            zip_files = [f for f in os.listdir(shared_path) if f.endswith('.zip')]
            if not zip_files:
                print("未找到zip文件")
                self.download_data_button.config(state='normal', text='下载数据')
                return
            # 取最新的（按文件名排序）
            latest_zip = sorted(zip_files, reverse=True)[0]
            zip_name = latest_zip
            print(f"最新数据包: {zip_name}")

            # 下载到 historical_data 目录
            save_dir = os.path.join(CURRENT_DIR, "historical_data")
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
            zip_path = os.path.join(save_dir, zip_name)
            print(f"正在复制到: {zip_path}")
            self.download_data_button.config(text='正在复制数据包...')
            # 复制ZIP文件到本地
            shutil.copy(os.path.join(shared_path, zip_name), zip_path)
            print("复制完成，正在解压...")
            self.download_data_button.config(text='正在解压数据包...')

            # 解压并覆盖
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                parent_dir = os.path.dirname(save_dir)  # 获取压缩包的上一级目录
                zip_ref.extractall(parent_dir)
            print(f"解压完成！文件已解压到: {parent_dir}")
            tk.messagebox.showinfo("提示", f"数据包 {zip_name} 复制并解压完成！")
        except FileNotFoundError as e:
            print(f"共享路径无效或无法访问: {e}")
            tk.messagebox.showerror("错误", f"请检查共享路径或网络连接: {shared_path}")
        except Exception as e:
            print(f"复制或解压失败: {e}")
            tk.messagebox.showerror("错误", f"复制或解压失败: {e}")
        finally:
            # 恢复按钮状态
            self.download_data_button.config(state='normal', text='下载数据')

    def save_analysis_progress(self, progress_data):
        """保存分析进度"""
        progress_path = os.path.join(CURRENT_DIR, "analysis_progress.json")
        
        try:
            with open(progress_path, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, indent=2)
            logging.info("已保存分析进度")
        except Exception as e:
            logging.error(f"保存分析进度失败: {e}")

    def load_analysis_progress(self):
        """加载分析进度"""
        progress_path = os.path.join(CURRENT_DIR, "analysis_progress.json")
        
        try:
            if os.path.exists(progress_path):
                with open(progress_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logging.error(f"加载分析进度失败: {e}")
        return {}


def main():
    root = tk.Tk()
    app = StockApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
